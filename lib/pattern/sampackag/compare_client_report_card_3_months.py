"""
Compares Client Report Card (3 months) values between UI and calculated results, and generates CSV, Excel, JSON, and HTML reports.
"""

import json
import csv
import re
import openpyxl
import os
import logging
from openpyxl.styles import Font, Alignment, PatternFill
from datetime import datetime
from dotenv import load_dotenv
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")

# Constants
DEFAULT_OUTPUT_FOLDER = create_folder_file_path(base_folder_name="Individual_Reports",
                                tenant_name=config.database_name)
os.makedirs(DEFAULT_OUTPUT_FOLDER, exist_ok=True)
Tenant = config.database_name
store = config.store_name
role = config.role


def clean_number(value):
    """Remove formatting and convert to float if possible."""
    if value is None or value == "":
        return "Missing"
    if isinstance(value, str):
        value = re.sub(r"[$%,]", "", value)  # Remove $, %, and ,
        if re.match(r"\(.*\)", value):  # Check if value is in parentheses (negative number)
            value = "-" + value.strip("()")  # Convert (2) to -2
    try:
        return float(value)
    except ValueError:
        return value  # Return as is if not convertible


def extract_main_values(data):
    """Extract main KPI fields like Monthly FOPC, Total, ROI, etc., ensuring a dictionary structure."""
    keys_to_extract = ["Monthly FOPC", "Monthly DMS", "Total", "ROI", "Total Pts & Lbr GP Change", "Repair ELR Change"]

    main_values = {}
    for key in keys_to_extract:
        value = clean_number(data.get(key, "Missing"))
        main_values[key] = {
            # "Second Month": value
            "3 MTH Avg (Baseline)": value
        }

    return main_values


def extract_kpis_from_dict(data):
    """Extract KPI values from the dictionary structure."""
    kpis = {}
    for kpi, values in data.get("KPIs", {}).items():
        if isinstance(values, list) and len(values) == 6:  # Handle list-based KPIs
            kpis[kpi] = {
                "3 MTH Avg (Baseline)": clean_number(values[0]),
                "Last Month": clean_number(values[1]),
                "Variance": clean_number(values[2]),
                "Prior Annual Pace": clean_number(values[3]),
                "Annual Pace": clean_number(values[4]),
                "Variance Annualized": clean_number(values[5]),
            }
        elif isinstance(values, dict):  # Handle dictionary-based KPIs
            kpis[kpi] = {
                "3 MTH Avg (Baseline)": clean_number(values.get("3 MTH Avg (Baseline)", "Missing")),
                "Last Month": clean_number(values.get("Last Month", "Missing")),
                "Variance": clean_number(values.get("Variance", "Missing")),
                "Prior Annual Pace": clean_number(values.get("Prior Annual Pace", "Missing")),
                "Annual Pace": clean_number(values.get("Annual Pace", "Missing")),
                "Variance Annualized": clean_number(values.get("Variance Annualized", "Missing")),
            }
    return kpis


def extract_category_kpis(data, category):
    """Extract KPIs from Competitive, Maintenance, and Repair sections."""
    kpis = {}
    category_data = data.get(category, {})
    for kpi, values in category_data.items():
        kpis[f"{category} - {kpi}"] = {
            "3 MTH Avg (Baseline)": clean_number(values.get("3 MTH Avg (Baseline)", "Missing")),
            "Last Month": clean_number(values.get("Last Month", "Missing")),
            "Variance": clean_number(values.get("Variance", "Missing")),
            "Prior Annual Pace": clean_number(values.get("Prior Annual Pace", "Missing")),
            "Annual Pace": clean_number(values.get("Annual Pace", "Missing")),
            "Variance Annualized": clean_number(values.get("Variance Annualized", "Missing")),
        }
    return kpis


# Helpers
def sanitize(name):
    return name.replace(" ", "-")


def get_output_folder():
    return f"Playwright-report-{sanitize(Tenant)}_{sanitize(store)}_{sanitize(role)}"


def generate_playwright_style_html(html_path: str, json_report_data: list) -> None:
    """
    Generates an HTML report from comparison results.
    """
    from collections import defaultdict

    passed = sum(1 for entry in json_report_data if entry["match"])
    failed = len(json_report_data) - passed
    total = len(json_report_data)

    # Group by section prefix
    sectioned_data = defaultdict(list)
    for entry in json_report_data:
        kpi = entry["kpi"]
        if " - " in kpi:
            section, name = kpi.split(" - ", 1)
        else:
            section, name = "KPI", kpi
        entry["clean_kpi"] = name
        sectioned_data[section].append(entry)

    html_template = f"""
    <!DOCTYPE html>
    <html lang=\"en\">
    <head>
        <meta charset=\"UTF-8\">
        <title>Client Report Card Three Months</title>
        <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">
        <style>
            body {{ padding: 20px; font-family: Arial, sans-serif; }}
            .badge-pass {{ background-color: #28a745; }}
            .badge-fail {{ background-color: #dc3545; }}
            .card-header {{ cursor: pointer; }}
        </style>
    </head>
    <body>
        <div class=\"container\">
            <h1 class=\"mb-4\">Client Report Card Three Months</h1>
            <div class=\"mb-4\">
                <strong>Tenant:</strong> {Tenant}<br>
                <strong>Store:</strong> {store}<br>
                <strong>Role:</strong> {role}<br>
                <strong>Generated At:</strong> {datetime.now().isoformat()}<br>
            </div>

            <div class=\"d-flex gap-3 mb-4\">
                <span class=\"badge bg-success\">Passed: {passed}</span>
                <span class=\"badge bg-danger\">Failed: {failed}</span>
                <span class=\"badge bg-secondary\">Total: {total}</span>
            </div>

            <div class=\"accordion\" id=\"reportAccordion\">
    """

    for s_idx, (section, entries) in enumerate(sectioned_data.items()):
        section_pass = all(entry["match"] for entry in entries)
        badge_class = "badge-pass" if section_pass else "badge-fail"
        badge_text = "Passed" if section_pass else "Failed"
        section_id = f"section{s_idx}"

        html_template += f"""
        <div class=\"accordion-item\">
            <h2 class=\"accordion-header\" id=\"heading-{section_id}\">
                <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#{section_id}\" aria-expanded=\"false\" aria-controls=\"{section_id}\">
                    {section} <span class=\"ms-3 badge {badge_class}\">{badge_text}</span>
                </button>
            </h2>
            <div id=\"{section_id}\" class=\"accordion-collapse collapse\" aria-labelledby=\"heading-{section_id}\" data-bs-parent=\"#reportAccordion\">
                <div class=\"accordion-body\">
        """

        for idx, entry in enumerate(entries):
            match = entry["match"]
            sub_badge = "badge-pass" if match else "badge-fail"
            sub_text = "Passed" if match else "Failed"
            sub_id = f"{section_id}-entry-{idx}"
            html_template += f"""
            <div class=\"card mb-2\">
                <div class=\"card-header\" data-bs-toggle=\"collapse\" data-bs-target=\"#{sub_id}\" aria-expanded=\"false\" style=\"cursor:pointer;\">
                    {entry['clean_kpi']} <span class=\"ms-2 badge {sub_badge}\">{sub_text}</span>
                </div>
                <div id=\"{sub_id}\" class=\"collapse\">
                    <div class=\"card-body\">
                        <strong>UI:</strong>
                        <pre>{json.dumps(entry['ui'], indent=2)}</pre>
                        <strong>Calculated:</strong>
                        <pre>{json.dumps(entry['calculated'], indent=2)}</pre>
                    </div>
                </div>
            </div>
            """

        html_template += """
                </div>
            </div>
        </div>
        """

    html_template += """
            </div>
        </div>
        <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>
    </body>
    </html>
    """
    with open(html_path, "w", encoding="utf-8") as f:
        f.write(html_template)


def compare_client_report_card_3_months(file1_path: str, file2_path: str) -> None:
    """
    Compare two JSON files and save the KPI comparison results in CSV, Excel, JSON, and HTML formats.
    """
    # Load JSON
    with open(file1_path, "r") as file1:
        results_data = json.load(file1)
    with open(file2_path, "r") as file2:
        ui_data = json.load(file2)

    # Extract KPIs
    main_values_first_file = extract_main_values(results_data)
    main_values_second_file = extract_main_values(ui_data)

    kpis_first_file = extract_kpis_from_dict(results_data)
    kpis_second_file = extract_kpis_from_dict(ui_data)

    for section in ["COMPETITIVE", "MAINTENANCE", "REPAIR"]:
        kpis_first_file.update(extract_category_kpis(results_data, section))
        kpis_second_file.update(extract_category_kpis(ui_data, section))

    output_data = []
    html_rows = ""
    json_report_data = []

    for kpi in set(kpis_first_file.keys()).union(set(kpis_second_file.keys())):
        first = kpis_first_file.get(
            kpi,
            {
                "3 MTH Avg (Baseline)": "Missing",
                "Last Month": "Missing",
                "Variance": "Missing",
                "Prior Annual Pace": "Missing",
                "Annual Pace": "Missing",
                "Variance Annualized": "Missing",
            },
        )
        second = kpis_second_file.get(
            kpi,
            {
                "3 MTH Avg (Baseline)": "Missing",
                "Last Month": "Missing",
                "Variance": "Missing",
                "Prior Annual Pace": "Missing",
                "Annual Pace": "Missing",
                "Variance Annualized": "Missing",
            },
        )

        is_match = all(
            first.get(metric, "Missing") == second.get(metric, "Missing")
            for metric in [
                "3 MTH Avg (Baseline)",
                "Last Month",
                "Variance",
                "Prior Annual Pace",
                "Annual Pace",
                "Variance Annualized",
            ]
        )

        row = [
            kpi,
            first["3 MTH Avg (Baseline)"],
            first["Last Month"],
            first["Variance"],
            first["Prior Annual Pace"],
            first["Annual Pace"],
            first["Variance Annualized"],
            second["3 MTH Avg (Baseline)"],
            second["Last Month"],
            second["Variance"],
            second["Prior Annual Pace"],
            second["Annual Pace"],
            second["Variance Annualized"],
            is_match,
        ]
        output_data.append(row)

        html_rows += (
            f"<tr style='background-color:{'white' if is_match else '#FFFF99'}'><td>{kpi}</td><td>{is_match}</td></tr>"
        )
        json_report_data.append({"kpi": kpi, "match": is_match, "ui": first, "calculated": second})

    for kpi in set(main_values_first_file.keys()).union(set(main_values_second_file.keys())):
        first_value = main_values_first_file.get(kpi, {"3 MTH Avg (Baseline)": "Missing"})["3 MTH Avg (Baseline)"]
        second_value = main_values_second_file.get(kpi, {"3 MTH Avg (Baseline)": "Missing"})["3 MTH Avg (Baseline)"]
        is_match = first_value == second_value

        row = [
            kpi,
            first_value,
            "N/A",
            "N/A",
            "N/A",
            "N/A",
            "N/A",
            second_value,
            "N/A",
            "N/A",
            "N/A",
            "N/A",
            "N/A",
            is_match,
        ]
        output_data.append(row)

        html_rows += (
            f"<tr style='background-color:{'white' if is_match else '#FFFF99'}'><td>{kpi}</td><td>{is_match}</td></tr>"
        )
        json_report_data.append(
            {
                "kpi": kpi,
                "match": is_match,
                "ui": {"3 MTH Avg (Baseline)": first_value},
                "calculated": {"3 MTH Avg (Baseline)": second_value},
            }
        )

    # Save CSV
    csv_path = os.path.join(DEFAULT_OUTPUT_FOLDER, "comparison_result_3_months_client_report.csv")
    with open(csv_path, "w", newline="", encoding="utf-8") as csvfile:
        writer = csv.writer(csvfile)
        # writer.writerow(["3 Month Client Report Card"])
        writer.writerow(
            [
                "KPI Name",
                "3 MTH Avg (Baseline)",
                "Last Month",
                "Variance",
                "Prior Annual Pace",
                "Annual Pace",
                "Variance Annualized",
                "3 MTH Avg (Baseline)",
                "Last Month",
                "Variance ",
                "Prior Annual Pace",
                "Annual Pace",
                "Variance Annualized",
                "Match (True/False)",
            ]
        )

        writer.writerows(output_data)

    # Save Excel
    xlsx_path = os.path.join(DEFAULT_OUTPUT_FOLDER, "comparison_client_report_3_months_highlighted.xlsx")
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Comparison Results"
    ws.merge_cells(start_row=1, start_column=1, end_row=1, end_column=14)
    ws.cell(row=1, column=1).value = "3 Month Client Report Card"
    ws.cell(row=1, column=1).font = Font(bold=True, size=14)
    ws.cell(row=1, column=1).alignment = Alignment(horizontal="center", vertical="center")
    # Define background fills
    ui_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")  # Light blue
    calc_fill = PatternFill(start_color="305496", end_color="305496", fill_type="solid")  # Light green
    # UI Title (row 3, columns 2 to 7)
    ws.merge_cells(start_row=3, start_column=2, end_row=3, end_column=7)
    ui_cell = ws.cell(row=3, column=2, value="UI")
    ui_cell.font = Font(bold=True, size=12)
    ui_cell.alignment = Alignment(horizontal="center", vertical="center")
    ui_cell.fill = ui_fill
    # Apply fill to entire merged range
    for col in range(2, 8):
        ws.cell(row=3, column=col).fill = ui_fill

    # Calculated Title (row 3, columns 8 to 13)
    ws.merge_cells(start_row=3, start_column=8, end_row=3, end_column=13)
    calc_cell = ws.cell(row=3, column=8, value="Calculated")
    calc_cell.font = Font(bold=True, size=12)
    calc_cell.alignment = Alignment(horizontal="center", vertical="center")
    calc_cell.fill = calc_fill
    # Apply fill to entire merged range
    for col in range(8, 14):
        ws.cell(row=3, column=col).fill = calc_fill
        # Apply fill to entire merged range
        for col in range(8, 14):
            ws.cell(row=3, column=col).fill = calc_fill
    # Read CSV
    with open(csv_path, "r", encoding="utf-8") as file:
        reader = csv.reader(file)
        headers = next(reader)

        # Write headers on row 2 with bold font
        for col_idx, header in enumerate(headers, start=1):
            cell = ws.cell(row=4, column=col_idx, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal="center", vertical="center")

        # Write data starting from row 4
        for row_idx, row in enumerate(reader, start=4):
            ws.append(row)

        # Highlight mismatches in yellow
        yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
        match_col = ws.max_column

        for row in ws.iter_rows(min_row=4, max_row=ws.max_row, min_col=match_col, max_col=match_col):
            for cell in row:
                if cell.value == "False":
                    for cell_to_fill in ws[cell.row]:
                        cell_to_fill.fill = yellow_fill

        # Save the workbook
        wb.save(xlsx_path)
        logging.info(f"Excel saved to {xlsx_path}")

    # # Save JSON report
    json_path = os.path.join(DEFAULT_OUTPUT_FOLDER, "client_report_card_three_months.json")
    with open(json_path, "w") as jf:
        json.dump(
            {
                "tenant": Tenant,
                "store": store,
                "role": role,
                "generatedAt": datetime.now().isoformat(),
                "results": json_report_data,
            },
            jf,
            indent=2,
        )
    logging.info(f"JSON report saved to {json_path}")

    # Save HTML report
    html_path = os.path.join(DEFAULT_OUTPUT_FOLDER, "client_report_card_three_months.html")
    generate_playwright_style_html(html_path, json_report_data)
    logging.info(f"HTML report saved to {html_path}")
