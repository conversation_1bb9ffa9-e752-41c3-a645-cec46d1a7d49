import asyncio
import json
import os
import time
import glob
import traceback
import pandas as pd
import numpy as np
from datetime import datetime
from math import isfinite, isnan
from playwright.async_api import async_playwright
from concurrent.futures import ThreadPoolExecutor
import threading
from datetime import datetime
from collections import defaultdict
import re
from decimal import Decimal, ROUND_HALF_UP
import openpyxl
import openpyxl.cell
from openpyxl.styles import <PERSON><PERSON><PERSON><PERSON>, Align<PERSON>, Font
from openpyxl.utils import get_column_letter
import csv
import argparse
from lib.pattern.config import config
from lib.pattern.sampackag.db_handler.db_connector import allRevenueDetailsCPOverview
from dateutil.relativedelta import relativedelta
from datetime import timedelta



# Target months-years for drilling down (modify as needed)
# This will be set after argument parsing
TARGET_MONTHS_YEARS = None

# Configuration constants
MAX_CONCURRENT_BROWSERS = 3
BROWSER_TIMEOUT = 30000
AUTH_STATE_FILE = "auth_state.json"

namespace = {
    'ns': 'http://www.dmotorworks.com/service-repair-order-history'
}

def round_off(n, decimals=0):
    """Round off numbers with proper decimal handling"""
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal("1"), rounding=ROUND_HALF_UP) / multiplier)


def zero_sales_check(df, columns):
    total_sum = df[columns].sum().sum()
    return total_sum == 0


def get_month_date_range_from_target(target_date_str):
    """Get the start and end date for the target month"""
    # Parse the target date string
    target_date = datetime.strptime(target_date_str, "%Y-%m-%d")
    
    # Get first day of the target month
    month_start = target_date.replace(day=1)
    
    # Get last day of the target month
    month_end = month_start + relativedelta(months=1) - timedelta(days=1)
    
    return month_start, month_end

def calculate_working_days(start_date, end_date):
    """Calculate working days between two dates (excluding weekends)"""
    working_days = 0
    current_date = start_date
    while current_date <= end_date:
        if current_date.weekday() < 5:  # Monday is 0, Sunday is 6
            working_days += 1
        current_date += timedelta(days=1)
    return working_days
def process_target_month_data(all_revenue_details_df, month_start, month_end, working_days_month, advisor, tech, retail_flag, customer_pay_types, warranty_pay_types, columns_to_check):
    """Process data for the target month and return results"""
    month_start = month_start.date()
    month_end = month_end.date()   
    # Filter data for the specific target month
    month_data = all_revenue_details_df[
        (all_revenue_details_df['closeddate'] >= month_start) &
        (all_revenue_details_df['closeddate'] <= month_end)
    ]
    
    print(f"Target month data shape: {month_data.shape}")
    
    if month_data.empty:
        print("No data found for the target month")
        return None
    
    # Apply existing filtering logic
    filtered_df = month_data[
        (month_data['department'] == 'Service') & 
        (month_data['hide_ro'] != True)
    ]
    
    if filtered_df.empty:
        print("No service department data found for the target month")
        return None
    
    filtered_df = filtered_df.copy()
    filtered_df['unique_ro_number'] = filtered_df['ronumber'].astype(str) + '_' + filtered_df['closeddate'].astype(str)
    
    # Initialize variables
    labor_revenue = 0
    parts_revenue = 0
    combined_revenue = 0
    labor_gross_profit = 0
    parts_gross_profit = 0
    combined_gross_profit = 0
    labor_gross_profit_percentage = 0
    parts_gross_profit_percentage = 0
    combined_gross_profit_percentage = 0
    labor_sold_hours = 0
    labor_sold_hours_combined = 0
    
    # NEW VARIABLES - Adding the missing calculations
    total_labor_cost = 0
    total_parts_cost = 0
    labor_sale_customer_pay = 0
    
    # ... [existing group assignment logic remains the same] ...
    
    combined_revenue_details = filtered_df.copy()
    combined_revenue_details['group'] = pd.Series(dtype="string")
    
    # Create a temporary version for zero check without modifying the original data
    temp_revenue_details = combined_revenue_details.copy()
    temp_revenue_details.loc[temp_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0
    
    # Iterate through each unique RO number for group assignment
    for ro_number in combined_revenue_details['unique_ro_number'].unique():
        ro_specific_rows = temp_revenue_details[temp_revenue_details['unique_ro_number'] == ro_number]
        
        ro_specific_rows_C = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(customer_pay_types)]
        ro_specific_rows_W = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(warranty_pay_types)]
        
        zero_sales_C = zero_sales_check(ro_specific_rows_C, columns_to_check)
        zero_sales_W = zero_sales_check(ro_specific_rows_W, columns_to_check)
        
        if not ro_specific_rows_C.empty and not zero_sales_C:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'C'
        elif not ro_specific_rows_W.empty and not zero_sales_W:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'W'
        else:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'I'
    
    # Apply filters based on advisor and tech conditions
    if advisor == {'all'} and tech == {'all'}:
        matching_ro_numbers = combined_revenue_details['unique_ro_number'].unique()
    elif advisor != {'all'} and tech == {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[combined_revenue_details['serviceadvisor'].astype(str).isin(advisor), 'unique_ro_number'].unique()
    elif advisor == {'all'} and tech != {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[combined_revenue_details['lbrtechno'].astype(str).isin(tech), 'unique_ro_number'].unique()
    elif advisor != {'all'} and tech != {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[(combined_revenue_details['serviceadvisor'].astype(str).isin(advisor)) & 
            (combined_revenue_details['lbrtechno'].astype(str).isin(tech)), 'unique_ro_number'].unique()
    
    # Applying the Advisor and tech filter conditions
    combined_revenue_details = combined_revenue_details[combined_revenue_details['unique_ro_number'].isin(matching_ro_numbers)]
    combined_revenue_details = combined_revenue_details.reset_index(drop=True)
    combined_revenue_details.loc[combined_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0
    
    # RO Counts
    Scorecard_10_CP = combined_revenue_details.loc[combined_revenue_details['group'] == 'C', 'unique_ro_number'].nunique()
    Scorecard_10_Wty = combined_revenue_details.loc[combined_revenue_details['group'] == 'W', 'unique_ro_number'].nunique()
    Scorecard_10_Int = combined_revenue_details.loc[combined_revenue_details['group'] == 'I', 'unique_ro_number'].nunique()
    all_unique_ros = Scorecard_10_CP + Scorecard_10_Wty + Scorecard_10_Int
    
    # Average ROs per day
    Average_ROs_Per_Day = round_off(all_unique_ros / working_days_month) if working_days_month > 0 else 0
    
    # ENHANCED CP CALCULATIONS - Following the second code pattern
    # Pre-compute zero-value mask for efficient filtering
    zero_values_mask = (
        (combined_revenue_details['lbrsale'].fillna(0) == 0) &
        (combined_revenue_details['lbrsoldhours'].fillna(0) == 0) &
        (combined_revenue_details['prtextendedsale'].fillna(0) == 0) &
        (combined_revenue_details['prtextendedcost'].fillna(0) == 0)
    )
    
    # Create masks for customer pay and group C
    customer_pay_mask = combined_revenue_details['paytypegroup'].isin(customer_pay_types)
    group_C_mask = combined_revenue_details['group'] == 'C'
    cp_and_customer_mask = customer_pay_mask & group_C_mask
    
    # Filter CP job details
    list_of_paytypegroup_C = combined_revenue_details[cp_and_customer_mask]
    total_CP_revenue_details_df = pd.DataFrame(list_of_paytypegroup_C)
    total_CP_revenue_details_df = total_CP_revenue_details_df[~zero_values_mask[cp_and_customer_mask]]
    
    # Apply tech filter if needed
    if tech != {'all'}:
        tech_mask = total_CP_revenue_details_df['lbrtechno'].astype(str).isin(tech)
        total_CP_revenue_details_df = total_CP_revenue_details_df[tech_mask]
    
    # CALCULATE THE REQUIRED METRICS
    if not total_CP_revenue_details_df.empty:
        # Convert columns to numeric once and store for reuse
        numeric_columns = {}
        columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
        
        for col in columns_to_convert:
            numeric_columns[col] = pd.to_numeric(total_CP_revenue_details_df[col], errors='coerce').fillna(0)
        
        # 1. Labor Sale - Customer Pay
        labor_sale_customer_pay = numeric_columns['lbrsale'].sum()
        
        # 2. Total Labor Cost
        total_labor_cost = numeric_columns['lbrcost'].sum()
        
        # 3. Total Parts Sale
        total_parts_sale = numeric_columns['prtextendedsale'].sum()
        
        # 4. Total Parts Cost
        total_parts_cost = numeric_columns['prtextendedcost'].sum()
        
        # Additional calculations (existing logic)
        labor_sold_hours = numeric_columns['lbrsoldhours'].sum()
        
        # Calculate gross profits
        labor_gross_profit = labor_sale_customer_pay - total_labor_cost
        parts_gross_profit = total_parts_sale - total_parts_cost
        combined_gross_profit = labor_gross_profit + parts_gross_profit
        
        # Calculate percentages
        labor_gross_profit_percentage = round_off((labor_gross_profit / labor_sale_customer_pay) * 100, 1) if labor_sale_customer_pay != 0 else 0
        parts_gross_profit_percentage = round_off((parts_gross_profit / total_parts_sale) * 100, 1) if total_parts_sale != 0 else 0
        combined_revenue = labor_sale_customer_pay + total_parts_sale
        combined_gross_profit_percentage = round_off((combined_gross_profit / combined_revenue) * 100, 1) if combined_revenue != 0 else 0
        
        # Effective labor rate
        effective_labor_rate_CP = round_off(labor_sale_customer_pay / labor_sold_hours, 2) if labor_sold_hours != 0 else 0
        
        # Parts markup
        cp_parts_markup_CP = round_off(total_parts_sale / total_parts_cost, 4) if total_parts_cost != 0 else 0
        
        # Round the main values
        labor_revenue = round_off(labor_sale_customer_pay, 2)
        parts_revenue = round_off(total_parts_sale, 2)
        labor_sold_hours = round_off(labor_sold_hours, 2)
    
    else:
        print("No Customer Pay data available for calculations")
    
    # Continue with combined calculations for all pay types
    if not combined_revenue_details.empty:
        labor_sold_hours_combined_value = pd.to_numeric(combined_revenue_details['lbrsoldhours']).fillna(0).sum()
        labor_sold_hours_combined = round_off(labor_sold_hours_combined_value, 2)
        
        all_labor_sale = pd.to_numeric(combined_revenue_details['lbrsale']).fillna(0).sum()
        all_sold_hours = pd.to_numeric(combined_revenue_details['lbrsoldhours']).fillna(0).sum()
        effective_labor_rate_combined = round_off(all_labor_sale / all_sold_hours, 2) if all_sold_hours != 0 else 0
        
        part_extended_sale_combined = pd.to_numeric(combined_revenue_details['prtextendedsale']).fillna(0).sum()
        part_extended_cost_combined = pd.to_numeric(combined_revenue_details['prtextendedcost']).fillna(0).sum()
        cp_parts_markup_combined = round_off(part_extended_sale_combined / part_extended_cost_combined, 4) if part_extended_cost_combined != 0 else 0
    
    # Return enhanced result set
    return {
        "target_month": month_start.strftime('%Y-%m'),
        "target_month_name": month_start.strftime('%B %Y'),
        "working_days": working_days_month,
        "average_ros_per_day": Average_ROs_Per_Day,
        "total_ros": all_unique_ros,
        "ro_counts": {
            "customer_pay_ros": Scorecard_10_CP,
            "warranty_ros": Scorecard_10_Wty,
            "internal_ros": Scorecard_10_Int
        },
        # ENHANCED CUSTOMER PAY METRICS
        "customer_pay_metrics": {
            "labor_sale_customer_pay": round_off(labor_sale_customer_pay, 2),
            "total_labor_cost": round_off(total_labor_cost, 2),
            "total_parts_sale": round_off(total_parts_sale, 2),
            "total_parts_cost": round_off(total_parts_cost, 2),
            "labor_gross_profit": round_off(labor_gross_profit, 2),
            "parts_gross_profit": round_off(parts_gross_profit, 2),
            "combined_gross_profit": round_off(combined_gross_profit, 2),
            "labor_gross_profit_percentage": labor_gross_profit_percentage,
            "parts_gross_profit_percentage": parts_gross_profit_percentage,
            "combined_gross_profit_percentage": combined_gross_profit_percentage,
            "effective_labor_rate": effective_labor_rate_CP,
            "elr": effective_labor_rate_CP,
            "parts_markup": cp_parts_markup_CP,
            "labor_sold_hours": round_off(labor_sold_hours, 2),
            "parts_to_labor_ratio": round_off(total_parts_sale / labor_sale_customer_pay, 2) if labor_sale_customer_pay != 0 else 0
        },
        # Existing metrics (keeping for compatibility)
        "labor_revenue": labor_revenue,
        "parts_revenue": parts_revenue,
        "combined_revenue": round_off(combined_revenue, 2),
        "labor_gross_profit": round_off(labor_gross_profit, 2),
        "parts_gross_profit": round_off(parts_gross_profit, 2),
        "combined_gross_profit": round_off(combined_gross_profit, 2),
        "labor_gross_profit_percentage": labor_gross_profit_percentage,
        "parts_gross_profit_percentage": parts_gross_profit_percentage,
        "combined_gross_profit_percentage": combined_gross_profit_percentage,
        "labor_sold_hours": labor_sold_hours,
        "labor_sold_hours_combined": labor_sold_hours_combined
    }

def db_execution(target_date_str, advisor, tech, retail_flag, columns_to_check):
    """
    Handle database operations and execute month processing
    """   
    
    try:
        # Get target month date range
        month_start, month_end = get_month_date_range_from_target(target_date_str)
        working_days_month = calculate_working_days(month_start, month_end)
        
        print(f"Target month range: {month_start.date()} to {month_end.date()}")
        print(f"Working days in month: {working_days_month}")
        
        # Fetch all data from database
        print("Fetching data from database...")
        all_revenue_details_table_db_connect = allRevenueDetailsCPOverview()
        all_revenue_details_df = all_revenue_details_table_db_connect.getTableResult()
        
        if all_revenue_details_df.empty:
            print("ERROR: No data retrieved from database!")
            return None       
        
        
        # Convert date column and other preprocessing
        columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
        all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].replace(r'^\s*$', np.nan, regex=True)
        all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].apply(lambda x: pd.to_numeric(x.fillna(0), errors='coerce'))
        
        # Define customer and warranty pay types based on retail_flag
        if 'C' in retail_flag and not 'E' in retail_flag and not 'M' in retail_flag:
            customer_pay_types = {'C'}
            warranty_pay_types = {'W', 'F', 'M', 'E'}
        elif 'C' in retail_flag and not 'E' in retail_flag and 'M' in retail_flag:
            customer_pay_types = {'C', 'M'}
            warranty_pay_types = {'W', 'F', 'E'}
        elif 'C' in retail_flag and 'E' in retail_flag and not 'M' in retail_flag:
            customer_pay_types = {'C', 'E'}
            warranty_pay_types = {'W', 'F', 'M'}
        elif 'C' in retail_flag and 'E' in retail_flag and 'M' in retail_flag:
            customer_pay_types = {'C', 'E', 'M'}
            warranty_pay_types = {'W', 'F'}
               
        
        target_month_result = process_target_month_data(
            all_revenue_details_df, 
            month_start, 
            month_end, 
            working_days_month, 
            advisor, 
            tech, 
            retail_flag, 
            customer_pay_types, 
            warranty_pay_types, 
            columns_to_check
        )
        
        
        return target_month_result, customer_pay_types, warranty_pay_types
        
    except Exception as e:
        print(f"ERROR in db_execution: {str(e)}")
        print("=" * 60)
        print("DATABASE EXECUTION FAILED")
        print("=" * 60)
        return None, None, None

def db_calculation():
    """
    Main execution function for db calculation
    """    
    # Configuration variables
    columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
    retail_flag = {'C'}
    
    storeid = config.store_id
    realm = config.database_name   
    advisor_set = config.advisor
    tech_set = config.technician
    # target_month_year = config.target_month_year    
    
    # Process advisor configuration
    if isinstance(advisor_set, str):
        if advisor_set.lower() == 'all':
            advisor = {'all'}
        elif ',' in advisor_set:
            advisor = {x.strip() for x in advisor_set.split(',')}
        else:
            advisor = {advisor_set.strip()}
    else:
        advisor = {'all'}
    
    if advisor != {'all'}:
        advisor_id = next(iter(advisor))   
    else:
        advisor_id = 'all'
    
    # Process technician configuration
    if isinstance(tech_set, str):
        if tech_set.lower() == 'all':
            tech = {'all'}
        elif ',' in tech_set:
            tech = {x.strip() for x in tech_set.split(',')}
        else:
            tech = {tech_set.strip()}
    else:
        tech = {'all'}
    
    # Display configuration
    print(f"Processing Configuration:")
    print(f"  Target Month: {TARGET_MONTHS_YEARS[0]}")
    print(f"  Store ID: {storeid}")
    print(f"  Realm: {realm}")
    print(f"  Advisor filter: {advisor}")
    print(f"  Technician filter: {tech}")
    print(f"  Retail flag: {retail_flag}")
    print("-" * 80)
    
    # Execute database operations and processing
    target_date_str = TARGET_MONTHS_YEARS[0]
    target_month_result, customer_pay_types, warranty_pay_types = db_execution(
        target_date_str, advisor, tech, retail_flag, columns_to_check
    )
    
    # Process results
    if target_month_result:
        print("\n" + "=" * 80)
        print("RESULTS PROCESSING")
        print("=" * 80)
        
        # Create the final result set for the target month only
        final_result_set = {
            "analysis_info": {
                "target_month": target_date_str,
                "analysis_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "store_id": storeid,
                "realm": realm,
                "advisor_filter": list(advisor),
                "technician_filter": list(tech),
                "customer_pay_types": list(customer_pay_types),
                "warranty_pay_types": list(warranty_pay_types)
            },
            "target_month_results": target_month_result
        }
        
        # Write results to JSON file
        output_filename = "chart_processing_results/db_calculated_value.json"
        with open(output_filename, 'w', encoding='utf-8') as json_file:
            json.dump(final_result_set, json_file, indent=4, ensure_ascii=False)
        
        print(f"\nTarget month CP overview data written successfully to {output_filename}")
        
        # Display summary
        print(f"\nTarget Month Summary for {target_month_result['target_month_name']}:")
        print(f"  Total Revenue: ${target_month_result['combined_revenue']:,.2f}")
        print(f"  Total Gross Profit: ${target_month_result['combined_gross_profit']:,.2f}")
        print(f"  GP Percentage: {target_month_result['combined_gross_profit_percentage']}%")
        print(f"  Total ROs: {target_month_result['total_ros']}")
        print(f"    - Customer Pay ROs: {target_month_result['ro_counts']['customer_pay_ros']}")
        print(f"    - Warranty ROs: {target_month_result['ro_counts']['warranty_ros']}")
        print(f"    - Internal ROs: {target_month_result['ro_counts']['internal_ros']}")
        print(f"  Average ROs per day: {target_month_result['average_ros_per_day']}")
        print(f"  Working days: {target_month_result['working_days']}")
        print(f"  Labor sold hours: {target_month_result['labor_sold_hours']}")
        
    else:
        print("\n" + "=" * 80)
        print("NO DATA RESULTS PROCESSING")
        print("=" * 80)
        
        print(f"No data available for target month {target_date_str}")
        
    
    print("\n" + "=" * 80)
    print("CP OVERVIEW ANALYSIS - MAIN EXECUTION COMPLETED")
    print("=" * 80)

def normalize_month_year(month_year_str):
    """
    Normalize different month-year formats to a standard format for comparison
    """
    if not month_year_str:
        return None
    
    month_year_str = str(month_year_str).strip()
    
    # Handle apostrophe format like "Jun'2024"
    month_year_str = month_year_str.replace("'", " ")
    
    # Common patterns to match
    patterns = [
        r'(\w{3})\s+(\d{4})',  # Jan 2024
        r'(\w{3})\s*(\d{4})',  # Jan2024
        r'(\d{1,2})/(\d{4})',  # 1/2024
        r'(\d{1,2})-(\d{4})',  # 1-2024
        r'(\w+)\s+(\d{4})',    # January 2024
    ]
    
    month_map = {
        'jan': 'Jan', 'january': 'Jan',
        'feb': 'Feb', 'february': 'Feb',
        'mar': 'Mar', 'march': 'Mar',
        'apr': 'Apr', 'april': 'Apr',
        'may': 'May',
        'jun': 'Jun', 'june': 'Jun',
        'jul': 'Jul', 'july': 'Jul',
        'aug': 'Aug', 'august': 'Aug',
        'sep': 'Sep', 'september': 'Sep',
        'oct': 'Oct', 'october': 'Oct',
        'nov': 'Nov', 'november': 'Nov',
        'dec': 'Dec', 'december': 'Dec'
    }
    
    for pattern in patterns:
        match = re.match(pattern, month_year_str, re.IGNORECASE)
        if match:
            month_part, year_part = match.groups()
            
            # Handle numeric month
            if month_part.isdigit():
                month_num = int(month_part)
                if 1 <= month_num <= 12:
                    month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                                 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
                    month_part = month_names[month_num - 1]
            else:
                # Handle text month
                month_part = month_map.get(month_part.lower(), month_part)
            
            return f"{month_part} {year_part}"
    
    return month_year_str

class AuthManager:
    """Handles authentication for the application"""
    
    def __init__(self):
        self.auth_state = None
        self.auth_file = AUTH_STATE_FILE
    
    def get_auth_state(self):
        """Get current auth state"""
        if not self.auth_state:
            self.load_auth_state()
        return self.auth_state
    
    def load_auth_state(self):
        """Load auth state from file"""
        try:
            if os.path.exists(self.auth_file):
                with open(self.auth_file, 'r') as f:
                    self.auth_state = json.load(f)
                print("Auth state loaded from file")
                return True
        except Exception as e:
            print(f"Could not load auth state: {e}")
        return False
    
    def save_auth_state(self, auth_state):
        """Save auth state to file"""
        try:
            with open(self.auth_file, 'w') as f:
                json.dump(auth_state, f, indent=2)
            self.auth_state = auth_state
            print("Auth state saved to file")
            return True
        except Exception as e:
            print(f"Could not save auth state: {e}")
            return False
    
    async def setup_authentication(self, playwright):
        """Setup authentication with login process"""
        print("Setting up authentication...")
        
        browser = await playwright.chromium.launch(
            headless=False,  # Set to True for headless mode
            args=['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu']
        )
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        page = await context.new_page()
        page.set_default_timeout(BROWSER_TIMEOUT)
        
        try:
            login_success = await self.perform_login(page)
            
            if login_success:
                # Save authentication state
                auth_state = await context.storage_state()
                self.save_auth_state(auth_state)
                print(" Authentication setup completed successfully")
                return True
            else:
                print(" Authentication setup failed")
                return False
                
        finally:
            await context.close()
            await browser.close()
    
    async def perform_login(self, page, max_retries=3):
        """Perform login with retry mechanism"""
        
        for attempt in range(max_retries):
            try:
                print(f"Login attempt {attempt + 1}/{max_retries}")
                
                # Navigate to login page
                print("Navigating to login page...")
                await page.goto("https://sampackag.fixedops.cc/auth/login?provenance=fopc", timeout=30000)
                # await page.wait_for_load_state("networkidle")

                # Click login button
                print("Clicking login button...")
                await page.wait_for_selector("button#login", timeout=10000)
                await page.click("button#login", force=True)

                # Fill credentials
                print("Filling username and password...")
                await page.wait_for_selector("input[name='username']", timeout=10000)
                await page.fill("input[name='username']", "<EMAIL>")
                await page.fill("input[name='password']", "123")
                await page.click("input#kc-login")
                # await page.wait_for_load_state("networkidle")

                # Select Store
                print("Selecting store...")
                await page.wait_for_selector("#store-select", timeout=30000)
                await page.click("#store-select")
                await page.wait_for_selector("role=option[name='Five Star Ford of North Richland Hills']", timeout=30000)
                await page.get_by_role("option", name='Five Star Ford of North Richland Hills').click()
                # await page.wait_for_load_state("networkidle")

                # Click "View Dashboard"
                print("Accessing dashboard...")
                await page.wait_for_selector("button#login", timeout=30000)
                await page.click("button#login")
                # await page.wait_for_load_state("networkidle")
                
                # Verify by navigating to target page
                await page.goto("https://sampackag.fixedops.cc/SpecialMetrics", timeout=30000)
                # await page.wait_for_load_state("networkidle")
                
                print(" Login completed successfully")
                return True
                
            except Exception as e:
                print(f" Login attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    print("Retrying in 5 seconds...")
                    await asyncio.sleep(5)
        
        return False
    
class MultiChartParallelProcessor:
    """Process multiple charts in parallel with separate browsers"""
    
    def __init__(self, max_browsers=4, auth_manager=None):
        self.max_browsers = max_browsers
        self.auth_manager = auth_manager or AuthManager()
    
    async def create_authenticated_browser_context(self, playwright, headless=False):
        """Create an authenticated browser context"""
        browser = await playwright.chromium.launch(
            headless=headless,
            args=['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu']
        )
        
        # Load auth state
        auth_state = self.auth_manager.get_auth_state()
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            storage_state=auth_state
        )
        
        page = await context.new_page()
        page.set_default_timeout(BROWSER_TIMEOUT)
        
        return browser, context, page


    async def discover_charts(self, page_url=None):
        """Discover all charts on the new component page with 12 bar charts"""
        page_url = page_url or "https://sampackag.fixedops.cc/SpecialMetrics"  # Replace with actual URL
        print(f"Discovering charts on new component page: {page_url}")
        
        async with async_playwright() as playwright:
            browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)
            
            try:
                await page.goto(page_url, timeout=50000)
                chart_found = False
                
                # Wait for the page to load and charts to be rendered
                selectors_to_try = [
                    'canvas.chartjs-render-monitor',
                    'canvas[class*="chartjs"]',
                    'canvas',
                    '[id*="chart"]',
                    '.react-grid-item canvas',
                    '[id*="chartContainterId"]'
                ]

                for selector in selectors_to_try:
                    try:
                        await page.wait_for_selector(selector, timeout=5000)
                        chart_found = True
                        print(f"✓ Found charts using selector: {selector}")
                        break
                    except:
                        continue
                        
                if not chart_found:
                    print("❌ No chart elements found with any selector")
                    return False
            
                # Wait longer for charts to fully render
                await asyncio.sleep(5)
                
                charts_info = await page.evaluate("""
                    () => {
                        console.log('Starting chart discovery...');
                        
                        // First, try to find all chart containers
                        const chartContainers = document.querySelectorAll('[id*="chartContainterId"]');
                        console.log('Found chart containers:', chartContainers.length);
                        
                        const chartsInfo = [];
                        
                        // Method 1: Find charts by container ID
                        chartContainers.forEach((container, containerIndex) => {
                            const containerId = container.id;
                            const chartId = containerId.replace('chartContainterId-', '');
                            
                            // Look for canvas in this container
                            const canvas = container.querySelector('canvas');
                            if (canvas) {
                                const rect = canvas.getBoundingClientRect();
                                
                                // Get chart title from card header
                                let chartTitle = `Chart ${chartId}`;
                                const cardHeader = container.querySelector(`#card-header-${chartId}`);
                                if (cardHeader) {
                                    const titleElement = cardHeader.querySelector('.MuiCardHeader-title');
                                    if (titleElement && titleElement.textContent.trim()) {
                                        chartTitle = titleElement.textContent.trim();
                                    }
                                }
                                
                                // Check if chart has data (no "divNoData" element)
                                const hasData = !container.querySelector('.divNoData');
                                
                                chartsInfo.push({
                                    canvasIndex: containerIndex,
                                    chartTitle: chartTitle,
                                    canvasId: canvas.id || `canvas-${chartId}`,
                                    canvasClass: canvas.className,
                                    containerId: containerId,
                                    chartId: chartId,
                                    position: {
                                        x: Math.round(rect.left),
                                        y: Math.round(rect.top),
                                        width: Math.round(rect.width),
                                        height: Math.round(rect.height)
                                    },
                                    visible: rect.width > 0 && rect.height > 0,
                                    isChartJs: canvas.classList.contains('chartjs-render-monitor'),
                                    hasData: hasData,
                                    detectionMethod: 'container-based'
                                });
                            }
                        });
                        
                        console.log('Charts found by container method:', chartsInfo.length);
                        
                        // Method 2: Find any remaining canvases that might have been missed
                        const allCanvases = document.querySelectorAll('canvas');
                        console.log('Total canvases found:', allCanvases.length);
                        
                        allCanvases.forEach((canvas, canvasIndex) => {
                            // Skip if this canvas is already in our list
                            const alreadyFound = chartsInfo.some(chart => 
                                chart.canvasId === canvas.id || 
                                (chart.canvasId.includes('canvas-') && canvas.closest(`#${chart.containerId}`))
                            );
                            
                            if (!alreadyFound) {
                                const rect = canvas.getBoundingClientRect();
                                
                                // Only include visible canvases with reasonable dimensions
                                if (rect.width > 50 && rect.height > 50) {
                                    // Try to find chart title
                                    let chartTitle = `Chart ${canvasIndex + 1}`;
                                    let chartId = null;
                                    let containerId = null;
                                    
                                    // Look for parent container
                                    let container = canvas.closest('.react-grid-item, .MuiCard-root, [id*="chart"], [class*="chart"]');
                                    if (container) {
                                        // Try to extract ID from container
                                        if (container.id && container.id.includes('chartContainterId')) {
                                            containerId = container.id;
                                            chartId = container.id.replace('chartContainterId-', '');
                                        }
                                        
                                        // Look for title
                                        const titleElement = container.querySelector('h1, h2, h3, h4, h5, h6, .title, [class*="title"], .MuiCardHeader-title');
                                        if (titleElement && titleElement.textContent.trim()) {
                                            chartTitle = titleElement.textContent.trim();
                                        }
                                    }
                                    
                                    // Check for data
                                    const hasData = !container || !container.querySelector('.divNoData');
                                    
                                    chartsInfo.push({
                                        canvasIndex: canvasIndex,
                                        chartTitle: chartTitle,
                                        canvasId: canvas.id || `canvas-fallback-${canvasIndex}`,
                                        canvasClass: canvas.className,
                                        containerId: containerId,
                                        chartId: chartId || `fallback-${canvasIndex}`,
                                        position: {
                                            x: Math.round(rect.left),
                                            y: Math.round(rect.top),
                                            width: Math.round(rect.width),
                                            height: Math.round(rect.height)
                                        },
                                        visible: rect.width > 0 && rect.height > 0,
                                        isChartJs: canvas.classList.contains('chartjs-render-monitor') || canvas.classList.contains('chartjs-render'),
                                        hasData: hasData,
                                        detectionMethod: 'canvas-based'
                                    });
                                }
                            }
                        });
                        
                        // Method 3: Look for react-grid-items that might contain charts
                        const gridItems = document.querySelectorAll('.react-grid-item');
                        console.log('Grid items found:', gridItems.length);
                        
                        gridItems.forEach((item, itemIndex) => {
                            const canvas = item.querySelector('canvas');
                            if (canvas) {
                                // Check if we already have this chart
                                const alreadyFound = chartsInfo.some(chart => 
                                    chart.canvasId === canvas.id || 
                                    chart.containerId === item.id
                                );
                                
                                if (!alreadyFound) {
                                    const rect = canvas.getBoundingClientRect();
                                    
                                    if (rect.width > 0 && rect.height > 0) {
                                        let chartTitle = `Grid Chart ${itemIndex + 1}`;
                                        let chartId = null;
                                        
                                        if (item.id && item.id.includes('chartContainterId')) {
                                            chartId = item.id.replace('chartContainterId-', '');
                                            
                                            const cardHeader = item.querySelector(`#card-header-${chartId}`);
                                            if (cardHeader) {
                                                const titleElement = cardHeader.querySelector('.MuiCardHeader-title');
                                                if (titleElement && titleElement.textContent.trim()) {
                                                    chartTitle = titleElement.textContent.trim();
                                                }
                                            }
                                        }
                                        
                                        const hasData = !item.querySelector('.divNoData');
                                        
                                        chartsInfo.push({
                                            canvasIndex: itemIndex,
                                            chartTitle: chartTitle,
                                            canvasId: canvas.id || `grid-canvas-${itemIndex}`,
                                            canvasClass: canvas.className,
                                            containerId: item.id,
                                            chartId: chartId || `grid-${itemIndex}`,
                                            position: {
                                                x: Math.round(rect.left),
                                                y: Math.round(rect.top),
                                                width: Math.round(rect.width),
                                                height: Math.round(rect.height)
                                            },
                                            visible: rect.width > 0 && rect.height > 0,
                                            isChartJs: canvas.classList.contains('chartjs-render-monitor') || canvas.classList.contains('chartjs-render'),
                                            hasData: hasData,
                                            detectionMethod: 'grid-based'
                                        });
                                    }
                                }
                            }
                        });
                        
                        console.log(`Total charts discovered: ${chartsInfo.length}`);
                        
                        // Sort by position (top to bottom, left to right)
                        chartsInfo.sort((a, b) => {
                            if (Math.abs(a.position.y - b.position.y) < 50) {
                                return a.position.x - b.position.x;
                            }
                            return a.position.y - b.position.y;
                        });
                        
                        return chartsInfo;
                    }
                """)
                
                print(f"📊 Found {len(charts_info)} charts on the new component page")
                
                # Group charts by their data availability
                charts_with_data = [chart for chart in charts_info if chart['hasData']]
                charts_no_data = [chart for chart in charts_info if not chart['hasData']]
                
                print(f"✅ Charts with data: {len(charts_with_data)}")
                print(f"❌ Charts with no data: {len(charts_no_data)}")
                
                # Display detailed info for each chart
                for i, chart in enumerate(charts_info):
                    status = "✅ HAS DATA" if chart['hasData'] else "❌ NO DATA"
                    method = chart.get('detectionMethod', 'unknown')
                    print(f"  {i+1}. Chart ID: {chart['chartId']} - {chart['chartTitle']} {status}")
                    print(f"     Container: {chart['containerId']}")
                    print(f"     Canvas: {chart['canvasId']} (Method: {method})")
                    print(f"     Position: {chart['position']}")
                    print(f"     Visible: {chart['visible']}, ChartJS: {chart['isChartJs']}")
                    print()
                
                # Debug: Also log all chart container IDs found on page
                container_ids = await page.evaluate("""
                    () => {
                        const containers = document.querySelectorAll('[id*="chartContainterId"]');
                        return Array.from(containers).map(c => c.id);
                    }
                """)
                print(f"All chart container IDs on page: {container_ids}")
                
                return charts_info
                
            except Exception as e:
                print(f"❌ Error discovering charts: {str(e)}")
                import traceback
                traceback.print_exc()
                return False
                
            finally:
                await context.close()
                await browser.close()
    async def find_matching_points_in_chart(self, page, chart_index, target_month_year,chart_id):
        """Find matching data points for a specific chart and target month-year"""
        try:
            print(f"Finding points in chart {chart_index} for target: {target_month_year}")
            
            matching_points = await page.evaluate("""
                (args) => {
                    const { chartIndex, targetMonthYear } = args;
                    const matchingPoints = [];
                    
                    try {
                        // Find all canvas elements
                        const canvases = document.querySelectorAll('canvas');
                        console.log(`Found ${canvases.length} canvas elements`);
                        
                        // Check if the requested chart index exists
                        if (chartIndex >= canvases.length) {
                            console.log(`Chart index ${chartIndex} out of range (max: ${canvases.length - 1})`);
                            return [];
                        }
                        
                        const canvas = canvases[chartIndex];
                        let chart = null;
                        
                        console.log(`Processing canvas ${chartIndex} for target: ${targetMonthYear}`);
                        
                        // Multiple methods to get chart instance
                        try {
                            // Method 1: Chart.getChart (Chart.js v3+)
                            if (typeof Chart !== 'undefined' && Chart.getChart) {
                                chart = Chart.getChart(canvas);
                                console.log(`Method 1 - Chart.getChart: ${chart ? 'Found' : 'Not found'}`);
                            }
                            
                            // Method 2: Chart.instances (Chart.js v2)
                            if (!chart && typeof Chart !== 'undefined' && Chart.instances) {
                                const instances = Object.values(Chart.instances);
                                chart = instances.find(instance => instance.canvas === canvas);
                                console.log(`Method 2 - Chart.instances: ${chart ? 'Found' : 'Not found'}`);
                            }
                            
                            // Method 3: Canvas._chart property (older versions)
                            if (!chart && canvas._chart) {
                                chart = canvas._chart;
                                console.log(`Method 3 - canvas._chart: Found`);
                            }
                            
                            // Method 4: Check for chart instance in canvas properties
                            if (!chart) {
                                const keys = Object.keys(canvas);
                                for (const key of keys) {
                                    if (key.includes('chart') || key.includes('Chart')) {
                                        chart = canvas[key];
                                        if (chart && chart.data) {
                                            console.log(`Method 4 - Found chart via property: ${key}`);
                                            break;
                                        }
                                    }
                                }
                            }
                            
                        } catch (e) {
                            console.warn(`Error getting chart instance for canvas ${chartIndex}:`, e);
                        }
                        
                        if (!chart) {
                            console.log(`No chart found for canvas ${chartIndex}`);
                            return [];
                        }
                        
                        // Validate chart structure
                        if (!chart.data || !chart.data.datasets) {
                            console.log(`Invalid chart data structure for canvas ${chartIndex}`);
                            return [];
                        }
                        
                        console.log(`Processing chart with ${chart.data.datasets.length} datasets`);
                        
                        const canvasRect = canvas.getBoundingClientRect();
                        console.log(`Canvas rect:`, canvasRect);
                        
                        // Get x-axis labels
                        const xLabels = chart.data.labels || [];
                        console.log(`X-axis labels:`, xLabels);
                        
                        // Helper function to check if label matches target
                        const isLabelMatch = (label, target) => {
                            if (!label || !target) return false;
                            
                            const labelStr = String(label).toLowerCase().trim();
                            const targetStr = String(target).toLowerCase().trim();
                            
                            // Direct match
                            if (labelStr === targetStr) return true;
                            
                            // Contains match
                            if (labelStr.includes(targetStr) || targetStr.includes(labelStr)) return true;
                            
                            // Month-year pattern matching (e.g., "Jan 2024", "January 2024", "01/2024")
                            const monthYearRegex = /(\w+)[\/\-\s]+(\d{4})/;
                            const labelMatch = labelStr.match(monthYearRegex);
                            const targetMatch = targetStr.match(monthYearRegex);
                            
                            if (labelMatch && targetMatch) {
                                const labelMonth = labelMatch[1];
                                const labelYear = labelMatch[2];
                                const targetMonth = targetMatch[1];
                                const targetYear = targetMatch[2];
                                
                                // Check if years match and months match (partial match allowed)
                                if (labelYear === targetYear && 
                                    (labelMonth.includes(targetMonth) || targetMonth.includes(labelMonth))) {
                                    return true;
                                }
                            }
                            
                            return false;
                        };
                        
                        // Process each dataset
                        for (let datasetIndex = 0; datasetIndex < chart.data.datasets.length; datasetIndex++) {
                            const dataset = chart.data.datasets[datasetIndex];
                            
                            if (!dataset.data || !Array.isArray(dataset.data)) {
                                console.log(`No data in dataset ${datasetIndex}`);
                                continue;
                            }
                            
                            console.log(`Processing dataset ${datasetIndex} with ${dataset.data.length} points`);
                            
                            // Process each data point
                            for (let pointIndex = 0; pointIndex < dataset.data.length; pointIndex++) {
                                const value = dataset.data[pointIndex];
                                const xLabel = xLabels[pointIndex] || `Point ${pointIndex}`;
                                
                                // Only process points that match the target month-year
                                if (!isLabelMatch(xLabel, targetMonthYear)) {
                                    continue;
                                }
                                
                                console.log(`Found matching point: ${xLabel} matches ${targetMonthYear}`);
                                
                                try {
                                    let screenX = null;
                                    let screenY = null;
                                    let canvasX = null;
                                    let canvasY = null;
                                    
                                    // Enhanced coordinate extraction
                                    try {
                                        const meta = chart.getDatasetMeta(datasetIndex);
                                        if (meta && meta.data && meta.data[pointIndex]) {
                                            const element = meta.data[pointIndex];
                                            
                                            // Check if coordinates are valid numbers
                                            if (typeof element.x === 'number' && !isNaN(element.x) && 
                                                typeof element.y === 'number' && !isNaN(element.y)) {
                                                canvasX = element.x;
                                                canvasY = element.y;
                                                screenX = canvasRect.left + element.x;
                                                screenY = canvasRect.top + element.y;
                                                console.log(`Element coordinates: canvas(${canvasX}, ${canvasY}) -> screen(${screenX}, ${screenY})`);
                                            }
                                        }
                                    } catch (e) {
                                        console.warn(`Could not get element position for point ${pointIndex}:`, e);
                                    }
                                    
                                    // Fallback: Use chart scales to calculate coordinates
                                    if ((canvasX === null || isNaN(canvasX)) && chart.scales) {
                                        try {
                                            // Find x and y scales
                                            const xScale = chart.scales.x || chart.scales['x-axis-0'] || chart.scales.xAxes?.[0] ||
                                                        Object.values(chart.scales).find(s => s.axis === 'x' || s.type === 'category' || s.type === 'time');
                                            const yScale = chart.scales.y || chart.scales['y-axis-0'] || chart.scales.yAxes?.[0] ||
                                                        Object.values(chart.scales).find(s => s.axis === 'y' || s.position === 'left');
                                            
                                            if (xScale && yScale && xScale.getPixelForValue && yScale.getPixelForValue) {
                                                // Get the actual y value
                                                let yValue = value;
                                                if (typeof value === 'object' && value !== null) {
                                                    yValue = value.y || value.value || value.data;
                                                }
                                                if (typeof yValue === 'string') {
                                                    yValue = parseFloat(yValue);
                                                }
                                                
                                                if (!isNaN(yValue)) {
                                                    canvasX = xScale.getPixelForValue(pointIndex);
                                                    canvasY = yScale.getPixelForValue(yValue);
                                                    
                                                    if (!isNaN(canvasX) && !isNaN(canvasY)) {
                                                        screenX = canvasRect.left + canvasX;
                                                        screenY = canvasRect.top + canvasY;
                                                        console.log(`Scale-based coordinates: canvas(${canvasX}, ${canvasY}) -> screen(${screenX}, ${screenY})`);
                                                    }
                                                }
                                            }
                                        } catch (e) {
                                            console.warn(`Error in scale-based calculation:`, e);
                                        }
                                    }
                                    
                                    // Final validation of coordinates
                                    const coordsValid = screenX !== null && screenY !== null && 
                                                    !isNaN(screenX) && !isNaN(screenY) &&
                                                    isFinite(screenX) && isFinite(screenY);
                                    
                                    // Handle different value formats
                                    let displayValue = value;
                                    if (typeof value === 'object' && value !== null) {
                                        displayValue = value.y || value.value || value.data || JSON.stringify(value);
                                    }
                                    
                                    const pointData = {
                                        canvasIndex: chartIndex,
                                        datasetIndex: datasetIndex,
                                        pointIndex: pointIndex,
                                        value: displayValue,
                                        xLabel: xLabel,
                                        screenX: screenX,
                                        screenY: screenY,
                                        canvasX: canvasX,
                                        canvasY: canvasY,
                                        datasetLabel: dataset.label || `Dataset ${datasetIndex}`,
                                        chartType: chart.config ? chart.config.type : 'unknown',
                                        coordinatesValid: coordsValid,
                                        targetMonthYear: targetMonthYear
                                    };
                                    
                                    matchingPoints.push(pointData);
                                    console.log(`Added matching point:`, pointData);
                                    
                                } catch (e) {
                                    console.warn(`Error processing matching point ${pointIndex}:`, e);
                                }
                            }
                        }
                        
                    } catch (e) {
                        console.error('Error in chart point extraction:', e);
                        return [];
                    }
                    
                    console.log(`Found ${matchingPoints.length} matching points for chart ${chartIndex} and target ${targetMonthYear}`);
                    return matchingPoints;
                }
            """, {'chartIndex': chart_index, 'targetMonthYear': target_month_year})
            
            print(f" Found {len(matching_points)} matching points in chart {chart_index} for {target_month_year}")
            return matching_points
            
        except Exception as e:
            print(f" Error finding matching points in chart {chart_index}: {str(e)}")
            return []

      
    
    async def create_chart_point_combinations(self, target_months_years):
        """Create combinations of charts and their matching points"""
        print("Creating chart-point combinations...")

        async with async_playwright() as playwright:
            browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)
            
            try:
                # Navigate to SpecialMetrics
                await page.goto("https://sampackag.fixedops.cc/SpecialMetrics", timeout=30000)
                # await page.wait_for_load_state("networkidle", timeout=15000)
                await asyncio.sleep(2)
                
                # Discover all charts
                charts_info = await self.discover_charts()
                print(f"charts_info:------------------------------------------------>########################## {charts_info}")
                if not charts_info:
                    print(" No charts found")
                    # Delete auth_state.json file when no charts are found
                    auth_state_path = "auth_state.json"
                    try:
                        if os.path.exists(auth_state_path):
                            os.remove(auth_state_path)
                            print(f"Deleted {auth_state_path} due to no charts found")
                        else:
                            print(f"{auth_state_path} not found to delete")
                    except Exception as e:
                        print(f" Error deleting {auth_state_path}: {e}")
                    return []
                
                chart_point_combinations = []
                charts_with_points = []
                
                # For each chart, find matching points for each target month-year
                for chart_info in charts_info:
                    chart_index = chart_info['canvasIndex']
                    container_id = chart_info.get('containerId', '')
                    chart_id = container_id.split('-')[-1] if container_id else None
                    chart_title = chart_info.get('chartTitle', f'Chart {chart_id}')
                    print(f"Processing Chart {chart_id}: {chart_title}")
                    
                    chart_total_points = 0
                    chart_combinations = []
                    
                    # Process each target month-year for this chart
                    for target_month_year in target_months_years:
                        print(f"  📅 Looking for data points matching: {target_month_year}")
                        
                        # Find matching points for this chart and target month-year
                        matching_points = await self.find_matching_points_in_chart(
                            page, chart_index, target_month_year,chart_id
                        )
                        
                        print(f"  Found {len(matching_points) if matching_points else 0} matching points for {target_month_year}")
                        
                        if matching_points:
                            # Create combination for this chart and target month-year
                            combination = {
                                'chart_index': f"chart_{chart_index}",
                                'chart_id': chart_id,
                                'chart_info': chart_info,
                                'target_month_year': target_month_year,
                                'matching_points': matching_points,
                                'processing_status': 'pending',
                                'points_count': len(matching_points)
                            }
                            chart_combinations.append(combination)
                            chart_total_points += len(matching_points)
                            
                            print(f"   Added combination: Chart {chart_index} - {target_month_year} ({len(matching_points)} points)")
                            print(f"   MATCHING#################################################################: Chart {chart_index} - {chart_id} ({matching_points} points)")
                        else:
                            print(f"   No matching points found for Chart {chart_index} - {target_month_year}")
                    
                    # Track charts with their point counts
                    if chart_combinations:
                        charts_with_points.append({
                            'chart_index': chart_index,
                            'chart_id': chart_id,
                            'chart_title': chart_title,
                            'total_points': chart_total_points,
                            'combinations': chart_combinations
                        })
                
                # Sort charts by total points (descending) to get charts with most points first
                charts_with_points.sort(key=lambda x: x['total_points'], reverse=True)
                
                # Take all charts and their combinations (all 12 combinations)
                for chart_data in charts_with_points:
                    chart_point_combinations.extend(chart_data['combinations'])
                    print(f"  Added Chart {chart_data['chart_index']}: {chart_data['chart_title']} ({chart_data['total_points']} points)")
                
                print(f"\nSummary: Created {len(chart_point_combinations)} chart-point combinations from {len(charts_with_points)} charts")
                
                # Print summary by chart
                chart_summary = {}
                for combo in chart_point_combinations:
                    chart_id = combo['chart_id']
                    if chart_id not in chart_summary:
                        chart_summary[chart_id] = 0
                    chart_summary[chart_id] += 1
                
                for chart_id, count in chart_summary.items():
                    print(f"  {chart_id}: {count} combinations")
                
                return chart_point_combinations
                
            except Exception as e:
                print(f" Error creating chart-point combinations: {str(e)}")
                return []
            
            finally:
                await context.close()
                await browser.close()
    async def process_chart_combination(self, combination,target_month_year, browser_id):
        """Process a single chart-point combination in its own browser"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]
    
        print(f"Browser {browser_id}: Processing {combination['chart_id']} - {combination['target_month_year']}")
        
        async with async_playwright() as playwright:
            browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)
            
            try:
                # Navigate to SpecialMetrics
                await page.goto("https://sampackag.fixedops.cc/SpecialMetrics", timeout=30000)
                # await page.wait_for_load_state("networkidle", timeout=15000)
                await asyncio.sleep(2)
                
                results = []
                
                # Process each matching point in this combination
                for point in combination['matching_points']:
                    try:
                        print()
                        print(f"🎯 Browser {browser_id}: Clicking point {point['xLabel']} (Value: {point['value']})")
                        
                        # Click on the data point
                        extracted_data = await self.click_and_extract_data(page, point,target_month_year)
                        result = {
                                'chart_id': combination['chart_id'],
                                'chart_title': combination['chart_info']['chartTitle'],
                                'target_month_year': combination['target_month_year'],
                                'clicked_point': point,
                                'extracted_data': extracted_data,
                                'timestamp': datetime.now().isoformat(),
                                'browser_id': browser_id,
                                'success': True
                            }
                            
                        results.append(result)
                        print(f" Browser {browser_id}: Successfully processed point {point['xLabel']}")
                        
                        # Wait before processing next point
                        await asyncio.sleep(3)
                      
                    except Exception as e:
                        print(f" Browser {browser_id}: Error processing point {point.get('x_label', 'unknown')}: {e}")
                        
                        error_result = {
                            'chart_id': combination['chart_id'],
                            'chart_title': combination['chart_info']['chartTitle'],
                            'target_month_year': combination['target_month_year'],
                            'clicked_point': point,
                            'error': str(e),
                            'timestamp': datetime.now().isoformat(),
                            'browser_id': browser_id,
                            'success': False
                        }
                        results.append(error_result)
                
                combination['processing_status'] = 'completed'
                combination['results'] = results
                
                print(f" Browser {browser_id}: Completed {combination['chart_id']} - {len(results)} results")
                return combination
                
            except Exception as e:
                print(f" Browser {browser_id}: Error processing combination {combination['chart_id']}: {e}")
                combination['processing_status'] = 'failed'
                combination['error'] = str(e)
                return combination
                
            finally:
                await context.close()
                await browser.close()

    async def click_chart_point(self, page, point):
        """Click on a chart data point"""
        try:
            # Check if we have valid coordinates
            if not point.get('coordinatesValid', False):
                print(f"Invalid coordinates for point {point['x_label']}")
                return False
            
            screen_x = point['screenX']
            screen_y = point['screenY']
            
            if not (screen_x and screen_y and not (isnan(float(screen_x)) or isnan(float(screen_y)))):
                print(f"Invalid screen coordinates: ({screen_x}, {screen_y})")
                return False
            
            # Click at the calculated coordinates
            await page.mouse.click(screen_x, screen_y)
            print(f"Clicked at coordinates ({screen_x}, {screen_y})")
            
            # Wait for any UI changes
            await asyncio.sleep(1)
            
            return True
            
        except Exception as e:
            print(f" Error clicking chart point: {e}")
            return False

    async def click_and_extract_data(self, page, point_data, target_month_year):
        """Click on a chart point and extract drilldown data"""
        result = {
            "target_month_year": target_month_year,
            "point_data": point_data,
            "click_success": False,
            "navigation_success": False,
            "extraction_data": None,
            "error": None,
            "processing_time": None,
            "screenshot_path": None
        }
        
        start_time = time.time()
        
        try:
            x_coord = point_data['screenX']
            y_coord = point_data['screenY']            
            # Validate coordinates
            if not (isinstance(x_coord, (int, float)) and isinstance(y_coord, (int, float))):
                result["error"] = "Invalid coordinates"
                return result            
            if not (0 <= x_coord <= 3000 and 0 <= y_coord <= 2000):
                result["error"] = f"Coordinates out of bounds: ({x_coord}, {y_coord})"
                return result            
            # Wait for page to be ready
            # await page.wait_for_load_state("networkidle", timeout=10000)
            await asyncio.sleep(1)            
            # Click on the point
            print(f"Clicking on point at ({x_coord}, {y_coord})")
            await page.mouse.move(x_coord, y_coord)
            await asyncio.sleep(0.5)
            await page.mouse.click(x_coord, y_coord)
            result["click_success"] = True            
            # Wait for navigation/modal
            await asyncio.sleep(3)            
            try:
                # await page.wait_for_load_state("networkidle", timeout=15000)
                result["navigation_success"] = True                
                # Extract data from drilldown page
                extraction_data = await self.extract_drilldown_data(page)
                result["extraction_data"] = extraction_data           
                
            except Exception as nav_error:
                result["error"] = f"Navigation error: {str(nav_error)}"                
        except Exception as e:
            result["error"] = f"Click error: {str(e)}"
        
        result["processing_time"] = time.time() - start_time
        return result
           
    async def extract_drilldown_data_flexible(self, page):
        """More flexible extraction that looks for title-value pairs"""
        try:
            print("Extracting drill-down page data (flexible approach)...")
            
            # await page.wait_for_load_state("networkidle")
            await asyncio.sleep(3)
            
            extraction_data = {
                "extraction_timestamp": datetime.now().isoformat(),
                "page_url": page.url,
                "data_pairs": [],
                "success": False,
                "error": None
            }
            
            # Method 1: Look for any h5 + h6 pairs (regardless of container structure)
            h5_elements = await page.query_selector_all('h5.MuiTypography-h5')
            h6_elements = await page.query_selector_all('h6.MuiTypography-subtitle1')
            
            print(f"Found {len(h5_elements)} h5 elements and {len(h6_elements)} h6 elements")
            
            # Try to pair them up by proximity or parent relationship
            for i, h5 in enumerate(h5_elements):
                try:
                    # Get the parent container of the h5
                    parent = await h5.query_selector('xpath=..')
                    if parent:
                        # Look for h6 in the same parent
                        h6_in_parent = await parent.query_selector('h6.MuiTypography-subtitle1')
                        if h6_in_parent:
                            title = (await h5.text_content()).strip()
                            value = (await h6_in_parent.text_content()).strip()
                            
                            pair_data = {
                                "index": i,
                                "title": title,
                                "value": value,
                                "extraction_method": "parent_container"
                            }
                            
                            extraction_data["data_pairs"].append(pair_data)
                            print(f"Extracted pair: {title} - {value}")
                            
                except Exception as e:
                    print(f"Error processing h5 element {i}: {e}")
                    continue
            
            # Method 2: If pairing fails, try sequential matching
            if not extraction_data["data_pairs"] and len(h5_elements) == len(h6_elements):
                print("Trying sequential h5-h6 matching...")
                for i, (h5, h6) in enumerate(zip(h5_elements, h6_elements)):
                    try:
                        title = (await h5.text_content()).strip()
                        value = (await h6.text_content()).strip()                        
                        pair_data = {
                            "index": i,
                            "title": title,
                            "value": value,
                            "extraction_method": "sequential"
                        }                        
                        extraction_data["data_pairs"].append(pair_data)
                        print(f"Extracted pair: {title} - {value}")                        
                    except Exception as e:
                        print(f"Error processing pair {i}: {e}")
                        continue            
            # Method 3: Look for any text that looks like monetary values
            if not extraction_data["data_pairs"]:
                print("Trying to find monetary patterns...")
                all_text = await page.text_content()                
                # Simple regex to find monetary values                
                money_pattern = r'\$[\d,]+\.?\d*'
                money_matches = re.findall(money_pattern, all_text)                
                if money_matches:
                    print(f"Found {len(money_matches)} monetary values: {money_matches}")
                    # This is a fallback - you'd need to implement logic to associate these with labels
            
            extraction_data["success"] = len(extraction_data["data_pairs"]) > 0            
            print(f"Flexible extraction success: {extraction_data['success']}")
            print(f"Data pairs found: {len(extraction_data['data_pairs'])}")            
            return extraction_data            
        except Exception as e:
            print(f" Error in flexible extraction: {e}")
            return {
                "extraction_timestamp": datetime.now().isoformat(),
                "success": False,
                "error": str(e)
            }
    async def extract_table_data(self, table_element):
        """Extract data from a table element"""
        try:
            # Get table headers
            headers = []
            header_elements = await table_element.query_selector_all('th, thead td')
            for header in header_elements:
                header_text = await header.text_content()
                if header_text:
                    headers.append(header_text.strip())
            
            # Get table rows
            rows = []
            row_elements = await table_element.query_selector_all('tbody tr, tr:not(:first-child)')
            for row in row_elements:
                row_data = []
                cell_elements = await row.query_selector_all('td, th')
                for cell in cell_elements:
                    cell_text = await cell.text_content()
                    row_data.append(cell_text.strip() if cell_text else '')
                
                if row_data:  # Only add non-empty rows
                    rows.append(row_data)
            
            return {
                'headers': headers,
                'rows': rows,
                'row_count': len(rows)
            }
            
        except Exception as e:
            print(f" Error extracting table data: {e}")
            return None

    async def implement_enhanced_single_legend_control(self, page):
        """Enhanced implementation for Chart.js single legend control with canvas detection and legend toggle"""

        enhanced_legend_control_script = """
        (function() {
            console.log('Implementing enhanced single legend control for Chart.js...');
            
            // Global variables
            let chartRotationIntervals = new Map();
            let currentActiveIndices = new Map();
            let chartInstances = new Map();
            let legendEnabled = new Map(); // Track legend state for each chart
            
            // Enhanced function to find Chart.js instances from canvas elements
            function findAllChartInstances() {
                const charts = [];
                
                // Method 1: Find canvases with Chart.js render monitor class
                const canvases = document.querySelectorAll('canvas.chartjs-render-monitor');
                console.log(`Found ${canvases.length} Chart.js canvas elements`);
                
                canvases.forEach((canvas, index) => {
                    // Try multiple ways to access the Chart.js instance
                    let chartInstance = null;
                    
                    // Method A: Direct chart property
                    if (canvas.chart) {
                        chartInstance = canvas.chart;
                    }
                    // Method B: Check Chart.js global instances
                    else if (window.Chart && window.Chart.instances) {
                        const chartId = canvas.getAttribute('data-chartjs-id') || 
                                    canvas.getAttribute('id') || 
                                    Object.keys(window.Chart.instances)[index];
                        if (chartId && window.Chart.instances[chartId]) {
                            chartInstance = window.Chart.instances[chartId];
                        }
                    }
                    // Method C: Search through all Chart instances
                    else if (window.Chart && window.Chart.instances) {
                        Object.values(window.Chart.instances).forEach(instance => {
                            if (instance.canvas === canvas) {
                                chartInstance = instance;
                            }
                        });
                    }
                    
                    if (chartInstance && chartInstance.data && chartInstance.data.datasets) {
                        const chartId = `chart-${index}-${Date.now()}`;
                        charts.push({
                            id: chartId,
                            canvas: canvas,
                            chart: chartInstance,
                            datasetsCount: chartInstance.data.datasets.length
                        });
                        
                        console.log(`Chart ${chartId}: Found with ${chartInstance.data.datasets.length} datasets`);
                    } else {
                        console.log(`Canvas ${index}: No Chart.js instance found`);
                    }
                });
                
                // Method 2: Fallback - check all Chart.js instances globally
                if (charts.length === 0 && window.Chart && window.Chart.instances) {
                    console.log('Fallback: Searching through global Chart instances...');
                    Object.entries(window.Chart.instances).forEach(([key, instance], index) => {
                        if (instance && instance.canvas && instance.data && instance.data.datasets) {
                            const chartId = `global-chart-${index}`;
                            charts.push({
                                id: chartId,
                                canvas: instance.canvas,
                                chart: instance,
                                datasetsCount: instance.data.datasets.length
                            });
                            console.log(` Global Chart ${chartId}: Found with ${instance.data.datasets.length} datasets`);
                        }
                    });
                }
                
                return charts;
            }
            
            // Function to create enhanced status indicator
            function createEnhancedStatusIndicator(chartData) {
                const { id, canvas, chart, datasetsCount } = chartData;
                
                if (datasetsCount <= 1) {
                    console.log(`ℹ️ Chart ${id}: Only ${datasetsCount} dataset(s), skipping indicator`);
                    return null;
                }
                
                // Find the chart container (look for parent with specific classes or create one)
                let chartContainer = canvas.closest('.MuiCard-root') || 
                                canvas.closest('.chart-container') || 
                                canvas.closest('div[class*="chart"]') ||
                                canvas.parentElement;
                
                if (!chartContainer) {
                    // Create a wrapper if none exists
                    chartContainer = document.createElement('div');
                    chartContainer.style.position = 'relative';
                    canvas.parentNode.insertBefore(chartContainer, canvas);
                    chartContainer.appendChild(canvas);
                }
                
                // Ensure container has relative positioning
                const computedStyle = window.getComputedStyle(chartContainer);
                if (computedStyle.position === 'static') {
                    chartContainer.style.position = 'relative';
                }
                
                // Create status indicator
                const statusContainer = document.createElement('div');
                statusContainer.id = `status-${id}`;
                statusContainer.className = 'chart-legend-status';
                statusContainer.style.cssText = `
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    background: linear-gradient(135deg, #1a237e, #3949ab);
                    color: white;
                    padding: 10px 14px;
                    border-radius: 20px;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    font-size: 12px;
                    font-weight: 600;
                    z-index: 1000;
                    min-width: 160px;
                    text-align: center;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.25);
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255,255,255,0.1);
                    transition: all 0.3s ease;
                `;
                
                // Create dataset info display
                const datasetInfo = document.createElement('div');
                datasetInfo.id = `dataset-info-${id}`;
                datasetInfo.style.cssText = `
                    font-size: 11px;
                    margin-bottom: 8px;
                    opacity: 0.9;
                `;
                
                // Create progress bar container
                const progressContainer = document.createElement('div');
                progressContainer.style.cssText = `
                    width: 100%;
                    height: 4px;
                    background: rgba(255,255,255,0.2);
                    border-radius: 2px;
                    margin: 8px 0;
                    overflow: hidden;
                `;
                
                const progressBar = document.createElement('div');
                progressBar.id = `progress-${id}`;
                progressBar.style.cssText = `
                    width: 0%;
                    height: 100%;
                    background: linear-gradient(90deg, #4caf50, #8bc34a);
                    border-radius: 2px;
                    transition: width 0.2s ease;
                `;
                
                progressContainer.appendChild(progressBar);
                
                // Create control buttons
                const controlsContainer = document.createElement('div');
                controlsContainer.style.cssText = `
                    display: flex;
                    gap: 6px;
                    justify-content: center;
                    margin-top: 8px;
                `;
                
                // Legend toggle button
                const legendToggleBtn = document.createElement('button');
                legendToggleBtn.id = `legend-toggle-${id}`;
                legendToggleBtn.innerHTML = '👁️';
                legendToggleBtn.title = 'Toggle legend visibility';
                legendToggleBtn.style.cssText = `
                    background: rgba(255,255,255,0.2);
                    border: 1px solid rgba(255,255,255,0.3);
                    border-radius: 6px;
                    padding: 4px 8px;
                    cursor: pointer;
                    font-size: 12px;
                    transition: all 0.2s ease;
                    color: white;
                `;
                
                // Play/Pause button
                const playPauseBtn = document.createElement('button');
                playPauseBtn.id = `playpause-${id}`;
                playPauseBtn.innerHTML = '⏸️';
                playPauseBtn.title = 'Toggle auto-rotation';
                playPauseBtn.style.cssText = `
                    background: rgba(255,255,255,0.2);
                    border: 1px solid rgba(255,255,255,0.3);
                    border-radius: 6px;
                    padding: 4px 8px;
                    cursor: pointer;
                    font-size: 12px;
                    transition: all 0.2s ease;
                    color: white;
                `;
                
                // Previous button
                const prevBtn = document.createElement('button');
                prevBtn.innerHTML = '⏮️';
                prevBtn.title = 'Previous dataset';
                prevBtn.style.cssText = playPauseBtn.style.cssText;
                
                // Next button
                const nextBtn = document.createElement('button');
                nextBtn.innerHTML = '⏭️';
                nextBtn.title = 'Next dataset';
                nextBtn.style.cssText = playPauseBtn.style.cssText;
                
                // Add hover effects
                [legendToggleBtn, playPauseBtn, prevBtn, nextBtn].forEach(btn => {
                    btn.addEventListener('mouseenter', () => {
                        btn.style.background = 'rgba(255,255,255,0.3)';
                        btn.style.transform = 'scale(1.05)';
                    });
                    btn.addEventListener('mouseleave', () => {
                        btn.style.background = 'rgba(255,255,255,0.2)';
                        btn.style.transform = 'scale(1)';
                    });
                });
                
                // Assemble the status indicator
                statusContainer.appendChild(datasetInfo);
                statusContainer.appendChild(progressContainer);
                controlsContainer.appendChild(legendToggleBtn);
                controlsContainer.appendChild(prevBtn);
                controlsContainer.appendChild(playPauseBtn);
                controlsContainer.appendChild(nextBtn);
                statusContainer.appendChild(controlsContainer);
                
                // Add to chart container
                chartContainer.appendChild(statusContainer);
                
                // Add event listeners
                legendToggleBtn.addEventListener('click', () => toggleLegend(id));
                playPauseBtn.addEventListener('click', () => toggleAutoRotation(id));
                nextBtn.addEventListener('click', () => switchToNextDataset(id));
                prevBtn.addEventListener('click', () => switchToPreviousDataset(id));
                
                console.log(` Status indicator created for chart ${id}`);
                return statusContainer;
            }
            
            // Function to toggle legend visibility
            function toggleLegend(chartId) {
                const chartData = chartInstances.get(chartId);
                if (!chartData) return;
                
                const chart = chartData.chart;
                const currentState = legendEnabled.get(chartId) ?? true;
                const newState = !currentState;
                
                // Update legend display
                if (chart.options.plugins && chart.options.plugins.legend) {
                    chart.options.plugins.legend.display = newState;
                } else {
                    // Initialize legend options if they don't exist
                    if (!chart.options.plugins) chart.options.plugins = {};
                    if (!chart.options.plugins.legend) chart.options.plugins.legend = {};
                    chart.options.plugins.legend.display = newState;
                }
                
                // Update legend state
                legendEnabled.set(chartId, newState);
                
                // Update button appearance
                const legendToggleBtn = document.getElementById(`legend-toggle-${chartId}`);
                if (legendToggleBtn) {
                    legendToggleBtn.innerHTML = newState ? '👁️' : '🙈';
                    legendToggleBtn.title = newState ? 'Hide legend' : 'Show legend';
                    legendToggleBtn.style.opacity = newState ? '1' : '0.6';
                }
                
                // Update chart
                chart.update('none');
                
                console.log(`🎯 Chart ${chartId}: Legend ${newState ? 'enabled' : 'disabled'}`);
            }
            
            // Enhanced function to toggle dataset visibility
            function setActiveDataset(chartData, activeIndex) {
                const { chart, id } = chartData;
                const datasets = chart.data.datasets;
                
                // Validate index
                if (activeIndex < 0 || activeIndex >= datasets.length) {
                    console.warn(`Invalid dataset index: ${activeIndex}`);
                    return;
                }
                
                // Hide all datasets except the active one
                datasets.forEach((dataset, index) => {
                    const meta = chart.getDatasetMeta(index);
                    if (meta) {
                        meta.hidden = (index !== activeIndex);
                    }
                });
                
                // Update legend to show only active dataset (if legend is enabled)
                const isLegendEnabled = legendEnabled.get(id) ?? true;
                if (isLegendEnabled && chart.options.plugins && chart.options.plugins.legend) {
                    if (!chart.options.plugins.legend.labels) {
                        chart.options.plugins.legend.labels = {};
                    }
                    chart.options.plugins.legend.labels.filter = function(legendItem) {
                        return legendItem.datasetIndex === activeIndex;
                    };
                }
                
                // Update chart without animation for better performance
                chart.update('none');
                
                console.log(`Chart ${id}: Activated dataset ${activeIndex} (${datasets[activeIndex].label || 'Unnamed'})`);
            }
            
            // Function to update status display
            function updateStatusDisplay(chartId, activeIndex, progress = 0) {
                const chartData = chartInstances.get(chartId);
                if (!chartData) return;
                
                const datasets = chartData.chart.data.datasets;
                const activeDataset = datasets[activeIndex];
                const datasetName = activeDataset.label || `Dataset ${activeIndex + 1}`;
                
                // Update dataset info
                const datasetInfo = document.getElementById(`dataset-info-${chartId}`);
                if (datasetInfo) {
                    datasetInfo.textContent = `${datasetName} (${activeIndex + 1}/${datasets.length})`;
                }
                
                // Update progress bar
                const progressBar = document.getElementById(`progress-${chartId}`);
                if (progressBar) {
                    progressBar.style.width = `${progress}%`;
                }
            }
            
            // Function to start automatic rotation
            function startAutoRotation(chartId) {
                const chartData = chartInstances.get(chartId);
                if (!chartData || chartData.datasetsCount <= 1) return;
                
                // Clear existing interval
                if (chartRotationIntervals.has(chartId)) {
                    clearInterval(chartRotationIntervals.get(chartId));
                }
                
                let currentIndex = currentActiveIndices.get(chartId) || 0;
                let progressCounter = 0;
                const rotationDuration = 10000; // 10 seconds
                const updateInterval = 100; // Update every 100ms
                const totalUpdates = rotationDuration / updateInterval;
                
                // Set initial state
                setActiveDataset(chartData, currentIndex);
                updateStatusDisplay(chartId, currentIndex, 0);
                
                const intervalId = setInterval(() => {
                    progressCounter++;
                    const progress = (progressCounter % totalUpdates) / totalUpdates * 100;
                    
                    // Update progress display
                    updateStatusDisplay(chartId, currentIndex, progress);
                    
                    // Switch dataset when progress completes
                    if (progressCounter % totalUpdates === 0 && progressCounter > 0) {
                        currentIndex = (currentIndex + 1) % chartData.datasetsCount;
                        currentActiveIndices.set(chartId, currentIndex);
                        setActiveDataset(chartData, currentIndex);
                    }
                }, updateInterval);
                
                chartRotationIntervals.set(chartId, intervalId);
                console.log(`Auto-rotation started for chart ${chartId}`);
            }
            
            // Function to stop automatic rotation
            function stopAutoRotation(chartId) {
                if (chartRotationIntervals.has(chartId)) {
                    clearInterval(chartRotationIntervals.get(chartId));
                    chartRotationIntervals.delete(chartId);
                    console.log(`⏹️ Auto-rotation stopped for chart ${chartId}`);
                }
            }
            
            // Function to toggle auto-rotation
            function toggleAutoRotation(chartId) {
                const playPauseBtn = document.getElementById(`playpause-${chartId}`);
                
                if (chartRotationIntervals.has(chartId)) {
                    stopAutoRotation(chartId);
                    if (playPauseBtn) playPauseBtn.innerHTML = '▶️';
                } else {
                    startAutoRotation(chartId);
                    if (playPauseBtn) playPauseBtn.innerHTML = '⏸️';
                }
            }
            
            // Function to switch to next dataset
            function switchToNextDataset(chartId) {
                const chartData = chartInstances.get(chartId);
                if (!chartData) return;
                
                const currentIndex = currentActiveIndices.get(chartId) || 0;
                const nextIndex = (currentIndex + 1) % chartData.datasetsCount;
                
                currentActiveIndices.set(chartId, nextIndex);
                setActiveDataset(chartData, nextIndex);
                updateStatusDisplay(chartId, nextIndex, 0);
                
                console.log(`⏭️ Chart ${chartId}: Manually switched to dataset ${nextIndex}`);
            }
            
            // Function to switch to previous dataset
            function switchToPreviousDataset(chartId) {
                const chartData = chartInstances.get(chartId);
                if (!chartData) return;
                
                const currentIndex = currentActiveIndices.get(chartId) || 0;
                const prevIndex = (currentIndex - 1 + chartData.datasetsCount) % chartData.datasetsCount;
                
                currentActiveIndices.set(chartId, prevIndex);
                setActiveDataset(chartData, prevIndex);
                updateStatusDisplay(chartId, prevIndex, 0);
                
                console.log(`⏮️ Chart ${chartId}: Manually switched to dataset ${prevIndex}`);
            }
            
            // Function to enable legend for a specific chart
            function enableLegend(chartId) {
                const chartData = chartInstances.get(chartId);
                if (!chartData) return;
                
                const chart = chartData.chart;
                
                // Enable legend
                if (!chart.options.plugins) chart.options.plugins = {};
                if (!chart.options.plugins.legend) chart.options.plugins.legend = {};
                chart.options.plugins.legend.display = true;
                
                legendEnabled.set(chartId, true);
                
                // Update button appearance
                const legendToggleBtn = document.getElementById(`legend-toggle-${chartId}`);
                if (legendToggleBtn) {
                    legendToggleBtn.innerHTML = '👁️';
                    legendToggleBtn.title = 'Hide legend';
                    legendToggleBtn.style.opacity = '1';
                }
                
                // Re-apply current dataset filter
                const currentIndex = currentActiveIndices.get(chartId) || 0;
                setActiveDataset(chartData, currentIndex);
                
                console.log(` Chart ${chartId}: Legend enabled`);
            }
            
            // Function to disable legend for a specific chart
            function disableLegend(chartId) {
                const chartData = chartInstances.get(chartId);
                if (!chartData) return;
                
                const chart = chartData.chart;
                
                // Disable legend
                if (!chart.options.plugins) chart.options.plugins = {};
                if (!chart.options.plugins.legend) chart.options.plugins.legend = {};
                chart.options.plugins.legend.display = false;
                
                legendEnabled.set(chartId, false);
                
                // Update button appearance
                const legendToggleBtn = document.getElementById(`legend-toggle-${chartId}`);
                if (legendToggleBtn) {
                    legendToggleBtn.innerHTML = '🙈';
                    legendToggleBtn.title = 'Show legend';
                    legendToggleBtn.style.opacity = '0.6';
                }
                
                chart.update('none');
                
                console.log(` Chart ${chartId}: Legend disabled`);
            }
            
            // Main setup function
            function setupEnhancedLegendControl() {
                console.log('Searching for Chart.js instances...');
                
                const foundCharts = findAllChartInstances();
                console.log(`Found ${foundCharts.length} Chart.js instances`);
                
                if (foundCharts.length === 0) {
                    console.log('No Chart.js instances found. Will retry...');
                    return false;
                }
                
                foundCharts.forEach(chartData => {
                    chartInstances.set(chartData.id, chartData);
                    currentActiveIndices.set(chartData.id, 0);
                    legendEnabled.set(chartData.id, true); // Initialize legend as enabled
                    
                    // Create status indicator
                    createEnhancedStatusIndicator(chartData);
                    
                    // Start auto-rotation
                    startAutoRotation(chartData.id);
                });
                
                console.log(` Enhanced legend control setup completed for ${foundCharts.length} charts`);
                return true;
            }
            
            // Retry mechanism for chart detection
            function initializeWithRetry() {
                let attempts = 0;
                const maxAttempts = 15;
                const retryInterval = 1000;
                
                function trySetup() {
                    attempts++;
                    console.log(`Setup attempt ${attempts}/${maxAttempts}`);
                    
                    if (setupEnhancedLegendControl() || attempts >= maxAttempts) {
                        if (attempts >= maxAttempts && chartInstances.size === 0) {
                            console.log(' Failed to find Chart.js instances after maximum attempts');
                            console.log('💡 Charts might be loaded dynamically. Try refreshing the page.');
                        }
                        return;
                    }
                    
                    setTimeout(trySetup, retryInterval);
                }
                
                trySetup();
            }
            
            // Cleanup function
            window.addEventListener('beforeunload', function() {
                chartRotationIntervals.forEach((intervalId) => {
                    clearInterval(intervalId);
                });
                chartRotationIntervals.clear();
                currentActiveIndices.clear();
                chartInstances.clear();
                legendEnabled.clear();
            });
            
            // Handle dynamic content changes
            const observer = new MutationObserver(function(mutations) {
                let shouldResetup = false;
                
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(node => {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                if (node.tagName === 'CANVAS' || node.querySelector('canvas')) {
                                    shouldResetup = true;
                                }
                            }
                        });
                    }
                });
                
                if (shouldResetup) {
                    console.log('DOM changes detected, re-initializing...');
                    setTimeout(() => setupEnhancedLegendControl(), 2000);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            // Start initialization
            initializeWithRetry();
            
            // Expose control functions globally
            window.chartLegendControl = {
                toggleLegend,
                enableLegend,
                disableLegend,
                toggleAutoRotation,
                switchToNextDataset,
                switchToPreviousDataset,
                stopAutoRotation,
                getChartInstances: () => Array.from(chartInstances.values()),
                getCurrentActiveIndex: (chartId) => currentActiveIndices.get(chartId),
                isLegendEnabled: (chartId) => legendEnabled.get(chartId) ?? true,
                manualSetup: setupEnhancedLegendControl
            };
            
            console.log('🎯 Enhanced Chart.js Legend Control initialized!');
            console.log('Available commands:');
            console.log('  - window.chartLegendControl.toggleLegend(chartId)');
            console.log('  - window.chartLegendControl.enableLegend(chartId)');
            console.log('  - window.chartLegendControl.disableLegend(chartId)');
            console.log('  - window.chartLegendControl.toggleAutoRotation(chartId)');
            
        })();
        """
        
        # Execute the enhanced JavaScript
        await page.evaluate(enhanced_legend_control_script)
    async def apply_enhanced_legend_control(self, page):
        """Apply the enhanced legend control script to the page with robust chart detection"""
        try:
            # Wait for charts to be loaded with multiple attempts
            print("Waiting for charts to load...")

            # Try multiple selectors to find charts
            chart_found = False
            selectors_to_try = [
                'canvas.chartjs-render-monitor',
                'canvas[class*="chartjs"]',
                'canvas',
                '[id*="chart"]'
            ]

            for selector in selectors_to_try:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    chart_found = True
                    print(f" Found charts using selector: {selector}")
                    break
                except:
                    continue

            if not chart_found:
                print("No chart elements found with any selector")
                return False

            await asyncio.sleep(3)  # Give charts time to fully initialize

            # Execute the comprehensive chart detection script
            result = await page.evaluate("""
                (function() {
                    console.log('=== COMPREHENSIVE CHART DETECTION ===');

                    // Check Chart.js availability
                    const chartJsAvailable = typeof Chart !== 'undefined';
                    console.log('Chart.js available:', chartJsAvailable);

                    if (!chartJsAvailable) {
                        // Try to find Chart.js in window object
                        const chartKeys = Object.keys(window).filter(key => key.toLowerCase().includes('chart'));
                        console.log('Potential chart objects:', chartKeys);
                        return false;
                    }

                    // Initialize chart instances map
                    window.legendControlInitialized = true;
                    window.chartInstances = new Map();

                    // Find all possible canvas elements
                    const allCanvases = document.querySelectorAll('canvas');
                    const chartCanvases = document.querySelectorAll('canvas.chartjs-render-monitor');
                    const anyChartCanvas = document.querySelectorAll('canvas[class*="chart"]');

                    console.log(`Canvas elements found:`);
                    console.log(`  - Total canvas: ${allCanvases.length}`);
                    console.log(`  - chartjs-render-monitor: ${chartCanvases.length}`);
                    console.log(`  - chart-related: ${anyChartCanvas.length}`);

                    // Use the most specific selector that found canvases
                    let canvasesToProcess = chartCanvases.length > 0 ? chartCanvases :
                                          anyChartCanvas.length > 0 ? anyChartCanvas : allCanvases;

                    console.log(`Processing ${canvasesToProcess.length} canvas elements`);

                    let successfullyRegistered = 0;

                    canvasesToProcess.forEach((canvas, index) => {
                        console.log(`\\n--- Processing Canvas ${index} ---`);

                        let chartInstance = null;
                        let detectionMethod = 'none';

                        // Method 1: Chart.getChart (Chart.js v3+)
                        try {
                            if (Chart.getChart) {
                                chartInstance = Chart.getChart(canvas);
                                if (chartInstance) {
                                    detectionMethod = 'Chart.getChart';
                                    console.log(`✓ Found via Chart.getChart`);
                                }
                            }
                        } catch (e) {
                            console.log(`✗ Chart.getChart failed:`, e.message);
                        }

                        // Method 2: Canvas chart property
                        if (!chartInstance) {
                            try {
                                if (canvas.chart) {
                                    chartInstance = canvas.chart;
                                    detectionMethod = 'canvas.chart';
                                    console.log(`✓ Found via canvas.chart`);
                                }
                            } catch (e) {
                                console.log(`✗ canvas.chart failed:`, e.message);
                            }
                        }

                        // Method 3: Canvas _chart property
                        if (!chartInstance) {
                            try {
                                if (canvas._chart) {
                                    chartInstance = canvas._chart;
                                    detectionMethod = 'canvas._chart';
                                    console.log(`✓ Found via canvas._chart`);
                                }
                            } catch (e) {
                                console.log(`✗ canvas._chart failed:`, e.message);
                            }
                        }

                        // Method 4: Search Chart.instances
                        if (!chartInstance) {
                            try {
                                if (Chart.instances) {
                                    Object.values(Chart.instances).forEach(instance => {
                                        if (instance && instance.canvas === canvas) {
                                            chartInstance = instance;
                                            detectionMethod = 'Chart.instances';
                                            console.log(`✓ Found via Chart.instances`);
                                        }
                                    });
                                }
                            } catch (e) {
                                console.log(`✗ Chart.instances search failed:`, e.message);
                            }
                        }

                        // Method 5: Check canvas data attributes or properties
                        if (!chartInstance) {
                            try {
                                // Look for any property that might contain a chart instance
                                const props = Object.getOwnPropertyNames(canvas);
                                for (const prop of props) {
                                    if (prop.includes('chart') || prop.includes('Chart')) {
                                        const value = canvas[prop];
                                        if (value && typeof value === 'object' && value.data && value.data.datasets) {
                                            chartInstance = value;
                                            detectionMethod = `canvas.${prop}`;
                                            console.log(`✓ Found via canvas.${prop}`);
                                            break;
                                        }
                                    }
                                }
                            } catch (e) {
                                console.log(`✗ Property search failed:`, e.message);
                            }
                        }

                        // Validate chart instance
                        if (chartInstance) {
                            console.log(`Chart instance found via: ${detectionMethod}`);

                            if (chartInstance.data && chartInstance.data.datasets && chartInstance.data.datasets.length > 0) {
                                // Use canvas ID or create consistent ID mapping
                                let chartId = canvas.id || `canvas-${index}`;

                                // Also register with alternative IDs for compatibility
                                const alternativeIds = [`chart_${index}`, `chart-${index}`, `canvas_${index}`];

                                window.chartInstances.set(chartId, {
                                    instance: chartInstance,
                                    canvas: canvas,
                                    detectionMethod: detectionMethod,
                                    originalLegendDisplay: chartInstance.options?.plugins?.legend?.display ?? true
                                });

                                // Register with alternative IDs
                                alternativeIds.forEach(altId => {
                                    window.chartInstances.set(altId, {
                                        instance: chartInstance,
                                        canvas: canvas,
                                        detectionMethod: detectionMethod,
                                        originalLegendDisplay: chartInstance.options?.plugins?.legend?.display ?? true
                                    });
                                });

                                console.log(` Registered ${chartId} (and alternatives: ${alternativeIds.join(', ')}):`);
                                console.log(`   - Datasets: ${chartInstance.data.datasets.length}`);
                                console.log(`   - Detection: ${detectionMethod}`);

                                chartInstance.data.datasets.forEach((dataset, dsIndex) => {
                                    console.log(`   - Dataset ${dsIndex}: "${dataset.label || 'Unnamed'}"`);
                                });

                                successfullyRegistered++;
                            } else {
                                console.log(`✗ Chart instance invalid (no datasets)`);
                            }
                        } else {
                            console.log(`✗ No chart instance found for canvas ${index}`);
                        }
                    });

                    console.log(`\\n=== DETECTION COMPLETE ===`);
                    console.log(`Successfully registered: ${successfullyRegistered} charts`);
                    console.log(`Chart IDs: [${Array.from(window.chartInstances.keys()).join(', ')}]`);

                    return successfullyRegistered > 0;
                })()
            """)

            if result:
                print(" Enhanced legend control applied successfully with comprehensive detection")
                return True
            else:
                print(" No chart instances found even with comprehensive detection")
                return False

        except Exception as e:
            print(f" Failed to apply enhanced legend control: {str(e)}")
            return False
    async def disable_all_legends(self, page):
        """Disable legends for all charts"""
        try:
            await page.evaluate("""
                if (window.chartInstances) {
                    window.chartInstances.forEach((chartData, chartId) => {
                        const chart = chartData.instance;
                        
                        // Disable legend
                        if (!chart.options.plugins) chart.options.plugins = {};
                        if (!chart.options.plugins.legend) chart.options.plugins.legend = {};
                        chart.options.plugins.legend.display = false;
                        
                        // Update chart
                        chart.update('none');
                        
                        console.log(`Legend disabled for ${chartId}`);
                    });
                }
            """)
            
            print(" All legends disabled")
            return True
            
        except Exception as e:
            print(f" Failed to disable legends: {str(e)}")
            return False
     
    async def enable_only_target_legend(self, page, chart_id, target_dataset_label):
        """Enable only the target dataset legend and disable all others"""
        try:
            result = await page.evaluate(f"""
            (function() {{
                console.log('Attempting to enable legend for chart: {chart_id}, dataset: {target_dataset_label}');

                if (!window.chartInstances) {{
                    console.log('chartInstances not found, attempting to reinitialize...');
                    return false;
                }}

                // Try multiple chart ID variations
                const chartIdVariations = ['{chart_id}', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;
                let actualChartId = null;

                for (const id of chartIdVariations) {{
                    if (window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        actualChartId = id;
                        console.log(`Found chart with ID: ${{id}}`);
                        break;
                    }}
                }}

                if (!chartData) {{
                    console.log('Chart not found with any ID variation');
                    console.log('Available charts:', Array.from(window.chartInstances.keys()));
                    return false;
                }}

                const chart = chartData.instance;

                if (!chart || !chart.data || !chart.data.datasets) {{
                    console.log('Invalid chart data structure for', actualChartId);
                    return false;
                }}

                // Find target dataset index with fuzzy matching
                let targetDatasetIndex = -1;
                chart.data.datasets.forEach((dataset, index) => {{
                    const datasetLabel = dataset.label || '';
                    const targetLabel = '{target_dataset_label}';

                    // Exact match first
                    if (datasetLabel === targetLabel) {{
                        targetDatasetIndex = index;
                        return;
                    }}

                    // Fuzzy match (contains or similar)
                    if (datasetLabel.includes(targetLabel) || targetLabel.includes(datasetLabel)) {{
                        targetDatasetIndex = index;
                        console.log(`Fuzzy match found: "${{datasetLabel}}" matches "${{targetLabel}}"`);
                    }}
                }});

                if (targetDatasetIndex === -1) {{
                    console.log('Target dataset not found: {target_dataset_label}');
                    console.log('Available datasets:', chart.data.datasets.map(d => d.label));

                    // Try to find any dataset and use the first one as fallback
                    if (chart.data.datasets.length > 0) {{
                        targetDatasetIndex = 0;
                        console.log('Using first dataset as fallback:', chart.data.datasets[0].label);
                    }} else {{
                        return false;
                    }}
                }}

                try {{
                    // Show only the target dataset
                    chart.data.datasets.forEach((dataset, index) => {{
                        const meta = chart.getDatasetMeta(index);
                        if (meta) {{
                            meta.hidden = (index !== targetDatasetIndex);
                        }}
                    }});

                    // Enable legend but filter to show only target dataset
                    if (!chart.options.plugins) chart.options.plugins = {{}};
                    if (!chart.options.plugins.legend) chart.options.plugins.legend = {{}};

                    chart.options.plugins.legend.display = true;
                    chart.options.plugins.legend.labels = {{
                        filter: function(legendItem) {{
                            return legendItem.datasetIndex === targetDatasetIndex;
                        }}
                    }};

                    // Update chart
                    chart.update('none');

                    const actualDatasetLabel = chart.data.datasets[targetDatasetIndex].label;
                    console.log('Successfully enabled legend for:', actualDatasetLabel, '(index:', targetDatasetIndex, ')');
                    return true;
                }} catch (error) {{
                    console.error('Error updating chart:', error);
                    return false;
                }}
            }})()
            """)

            if result:
                print(f" Enabled only legend for {target_dataset_label} in chart {chart_id}")
                return True
            else:
                print(f"Failed to enable legend for {target_dataset_label} in chart {chart_id}")
                return False

        except Exception as e:
            print(f" Error enabling legend for {target_dataset_label}: {str(e)}")
            return False

    async def debug_legend_control(self, page):
        """Debug function to check legend control setup"""
        try:
            debug_info = await page.evaluate("""
            (function() {
                const info = {
                    chartInstancesExists: !!window.chartInstances,
                    chartInstancesSize: window.chartInstances ? window.chartInstances.size : 0,
                    chartIds: window.chartInstances ? Array.from(window.chartInstances.keys()) : [],
                    chartDetails: []
                };

                if (window.chartInstances) {
                    window.chartInstances.forEach((chartData, chartId) => {
                        const chart = chartData.instance;
                        info.chartDetails.push({
                            id: chartId,
                            hasInstance: !!chart,
                            hasData: !!(chart && chart.data),
                            datasetCount: chart && chart.data && chart.data.datasets ? chart.data.datasets.length : 0,
                            datasetLabels: chart && chart.data && chart.data.datasets ? chart.data.datasets.map(d => d.label) : []
                        });
                    });
                }

                return info;
            })()
            """)
            
            for chart_detail in debug_info.get('chartDetails', []):
                chart_id = chart_detail.get('id', 'Unknown')
                dataset_count = chart_detail.get('datasetCount', 0)
                dataset_labels = chart_detail.get('datasetLabels', [])                
            return debug_info

        except Exception as e:
            print(f" Error debugging legend control: {str(e)}")
            return None

    async def click_data_point_for_drilldown(self, page, chart_id, point_data):
        """Click on a specific data point to trigger drilldown navigation"""
        try:
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            x_label = point_data.get('xLabel', 'Unknown')
            dataset_index = point_data.get('datasetIndex', 0)
            point_index = point_data.get('pointIndex', 0)
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')

            # First try using the pre-calculated screen coordinates
            if screen_x and screen_y and isinstance(screen_x, (int, float)) and isinstance(screen_y, (int, float)):
                try:
                    print(f"Clicking at pre-calculated coordinates: ({screen_x}, {screen_y})")
                    await page.mouse.click(screen_x, screen_y)
                    await asyncio.sleep(1)
                    return {
                        'success': True,
                        'method': 'pre_calculated_coordinates',
                        'position': {'x': screen_x, 'y': screen_y},
                        'dataset_label': dataset_label,
                        'x_label': x_label
                    }
                except Exception as coord_error:
                    print(f"Pre-calculated coordinates failed: {coord_error}")

            # Fallback to JavaScript-based clicking
            click_result = await page.evaluate(f"""
            (function() {{
                console.log('Attempting to click data point for chart: {chart_id}, dataset: {dataset_label}');

                if (!window.chartInstances) {{
                    console.log('chartInstances not found');
                    return {{ success: false, error: 'Chart instances not initialized' }};
                }}

                if (!window.chartInstances.has('{chart_id}')) {{
                    console.log('Chart {chart_id} not found');
                    console.log('Available charts:', Array.from(window.chartInstances.keys()));
                    return {{ success: false, error: 'Chart instance not found' }};
                }}

                const chartData = window.chartInstances.get('{chart_id}');
                const chart = chartData.instance;

                if (!chart || !chart.data || !chart.data.datasets) {{
                    console.log('Invalid chart data structure');
                    return {{ success: false, error: 'Invalid chart data' }};
                }}

                try {{
                    // Find target dataset index by label
                    let targetDatasetIndex = {dataset_index};
                    chart.data.datasets.forEach((dataset, index) => {{
                        if (dataset.label === '{dataset_label}') {{
                            targetDatasetIndex = index;
                        }}
                    }});

                    // Get dataset meta
                    const meta = chart.getDatasetMeta(targetDatasetIndex);
                    if (!meta || !meta.data || !meta.data[{point_index}]) {{
                        console.log('Data point not found at index: {point_index}');
                        return {{ success: false, error: 'Data point not found' }};
                    }}

                    // Get point element and position
                    const pointElement = meta.data[{point_index}];
                    const pointPosition = pointElement.getCenterPoint();
                    const canvas = chart.canvas;

                    // Create click event
                    const rect = canvas.getBoundingClientRect();
                    const clickEvent = new MouseEvent('click', {{
                        clientX: rect.left + pointPosition.x,
                        clientY: rect.top + pointPosition.y,
                        bubbles: true,
                        cancelable: true
                    }});

                    // Dispatch click event
                    canvas.dispatchEvent(clickEvent);

                    console.log('Successfully clicked data point: {x_label} from {dataset_label}');
                    return {{
                        success: true,
                        method: 'javascript_click',
                        position: pointPosition,
                        dataset_label: '{dataset_label}',
                        x_label: '{x_label}'
                    }};

                }} catch (error) {{
                    console.error('Error clicking data point:', error);
                    return {{ success: false, error: error.message }};
                }}
            }})()
            """)

            if click_result.get('success', False):
                print(f" Data point clicked for drilldown: {x_label} from {dataset_label}")
                return click_result
            else:
                print(f"Failed to click data point: {click_result.get('error', 'Unknown error')}")
                return click_result

        except Exception as e:
            print(f" Error clicking data point for drilldown: {str(e)}")
            return {'success': False, 'error': str(e)}

    async def wait_for_drilldown_navigation(self, page, timeout=10000):
        """Wait for navigation after clicking data point"""
        try:
            # Wait for navigation to complete
            # await page.wait_for_load_state("networkidle", timeout=timeout)
            await asyncio.sleep(2)            
            # Get current URL to confirm navigation
            current_url = page.url                      
            return {
                'success': True,
                'url': current_url,
                'navigation_completed': True
            }            
        except Exception as e:
            print(f"Navigation wait failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'navigation_completed': False
            }

    async def process_single_point_task_with_selective_legend(self, page, task, target_month_year):
        """Process a single point task with selective legend control and drilldown"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        chart_id = task['chart_id']
        point_data = task['point_data']
        try:
            point_label = point_data.get('xLabel', 'Unknown')
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')         
            # Step 1: Enable only the target legend, disable all others
            legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
            if not legend_enabled:
                print(f"{task_id}: Legend control failed, continuing anyway")
            # Wait for legend update
            await asyncio.sleep(1)
            # Debug: Check if the chart area is visible and clickable
            await self.debug_chart_clickability(page, chart_id, point_data)
            # Step 2: Click data point and wait for drilldown navigation
            click_result = None
            navigation_result = None
            # Get current URL before clicking
            initial_url = page.url
            print(f"{task_id}: Current URL before click: {initial_url}")
            # Method 1: Use pre-calculated coordinates (most reliable)
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')
            if screen_x and screen_y and isinstance(screen_x, (int, float)) and isinstance(screen_y, (int, float)):
                try:
                    await page.mouse.click(screen_x, screen_y)                   
                    await asyncio.sleep(3)  # Give time for navigation to start
                    # Check if URL changed
                    current_url = page.url
                    if current_url != initial_url:
                        print(f" {task_id}: Navigation detected to: {current_url}")
                        # Wait for page to fully load
                        try:
                            # await page.wait_for_load_state("networkidle", timeout=10000)
                            await asyncio.sleep(2)
                        except:
                            pass  # Continue even if load state fails

                        click_result = {
                            'success': True,
                            'method': 'pre_calculated_coordinates',
                            'coordinates': {'x': screen_x, 'y': screen_y}
                        }

                        navigation_result = {
                            'success': True,
                            'url': current_url,
                            'navigation_completed': True,
                            'initial_url': initial_url
                        }

                    else:
                        print(f"{task_id}: No navigation detected after coordinate click")
                        # Wait a bit more and check again
                        await asyncio.sleep(2)
                        current_url = page.url

                        if current_url != initial_url:
                            print(f" {task_id}: Delayed navigation detected to: {current_url}")
                            click_result = {'success': True, 'method': 'pre_calculated_coordinates'}
                            navigation_result = {'success': True, 'url': current_url, 'navigation_completed': True}
                        else:
                            print(f" {task_id}: No navigation after coordinate click")
                            click_result = {'success': False, 'error': 'No navigation detected after coordinate click'}
                            navigation_result = {'success': False, 'error': 'No URL change detected'}

                except Exception as coord_error:
                    print(f"{task_id}: Coordinate clicking failed: {coord_error}")
                    click_result = {'success': False, 'error': str(coord_error)}

            # Method 2: Fallback to JavaScript-based clicking
            if not click_result or not click_result.get('success', False):
                print(f"{task_id}: Trying JavaScript-based clicking...")

                try:
                    # Try JavaScript clicking
                    js_click_result = await self.click_data_point_for_drilldown(page, chart_id, point_data)

                    if js_click_result.get('success', False):
                        print(f"{task_id}: JavaScript click executed, waiting for navigation...")

                        # Wait for potential navigation
                        await asyncio.sleep(3)
                        # Check if URL changed
                        current_url = page.url
                        if current_url != initial_url:                           
                            # Wait for page to fully load
                            try:
                                # await page.wait_for_load_state("networkidle", timeout=10000)
                                await asyncio.sleep(2)
                            except:
                                pass
                            click_result = js_click_result
                            navigation_result = {
                                'success': True,
                                'url': current_url,
                                'navigation_completed': True,
                                'initial_url': initial_url
                            }
                        else:                           
                            # Wait a bit more and check again
                            await asyncio.sleep(2)
                            current_url = page.url
                            if current_url != initial_url:
                                print(f" {task_id}: Delayed JavaScript navigation detected to: {current_url}")
                                click_result = js_click_result
                                navigation_result = {'success': True, 'url': current_url, 'navigation_completed': True}
                            else:
                                click_result = {'success': False, 'error': 'JavaScript click no navigation'}
                                navigation_result = {'success': False, 'error': 'No URL change after JavaScript click'}
                    else:
                        click_result = js_click_result
                        navigation_result = {'success': False, 'error': 'JavaScript click failed'}

                except Exception as js_error:
                    print(f" {task_id}: JavaScript clicking failed: {js_error}")
                    click_result = {'success': False, 'error': str(js_error)}
                    navigation_result = {'success': False, 'error': str(js_error)}

            # Method 3: Fallback to simple click with longer wait
            if not click_result or not click_result.get('success', False):
                print(f"{task_id}: Trying simple click with extended wait...")
                try:
                    # Simple click without complex navigation detection
                    await page.mouse.click(screen_x, screen_y)
                    print(f"{task_id}: Simple click executed, waiting longer for navigation...")
                    # Wait longer for navigation
                    await asyncio.sleep(5)
                    # Check URL multiple times
                    for attempt in range(3):
                        current_url = page.url
                        if current_url != initial_url:
                            click_result = {
                                'success': True,
                                'method': 'simple_click_extended_wait',
                                'coordinates': {'x': screen_x, 'y': screen_y}
                            }
                            navigation_result = {
                                'success': True,
                                'url': current_url,
                                'navigation_completed': True,
                                'initial_url': initial_url
                            }
                            break

                        if attempt < 2:  # Don't wait after last attempt
                            await asyncio.sleep(2)

                    if not click_result or not click_result.get('success', False):
                        print(f" {task_id}: Simple click also failed to trigger navigation")
                        click_result = {'success': False, 'error': 'Simple click no navigation'}
                        navigation_result = {'success': False, 'error': 'No URL change after simple click'}

                except Exception as simple_error:
                    print(f" {task_id}: Simple clicking failed: {simple_error}")
                    click_result = {'success': False, 'error': str(simple_error)}
                    navigation_result = {'success': False, 'error': str(simple_error)}

            # Check if any clicking method succeeded
            if not click_result or not click_result.get('success', False):
                error_msg = click_result.get('error', 'All clicking methods failed') if click_result else 'No click result'
                print(f" {task_id}: Failed to click data point: {error_msg}")
                return {
                    'task_id': task_id,
                    'chart_id': chart_id,
                    'error': f"Failed to click data point: {error_msg}",
                    'success': False,
                    'legend_controlled': legend_enabled
                }
            # Check navigation result
            if not navigation_result or not navigation_result.get('success', False):
                print(f"{task_id}: Navigation failed, but click was successful - attempting data extraction anyway")
                # Try to extract data from current page
                current_url = page.url
                navigation_result = {
                    'success': False,
                    'url': current_url,
                    'navigation_completed': False,
                    'error': 'Navigation failed but click succeeded'
                }

            # Step 3: Extract data from the drilldown page
            extracted_data = None
            extraction_success = False
            # Only attempt extraction if navigation was successful
            if navigation_result and navigation_result.get('success', False):
                current_url = page.url
                # Check if we're on the expected drilldown page
                if "AnalyzeData" in current_url and "chartId=drillDown" in current_url:
                    print(f" {task_id}: On correct drilldown page, extracting data...")
                    extracted_data = await self.extract_data_from_drilldown_page(page, point_data, target_month_year)

                    # Check extraction success from the nested structure
                    extraction_success = False
                    if extracted_data and extracted_data.get('extraction_data'):
                        extraction_success = extracted_data['extraction_data'].get('success', False)
                    if extraction_success:
                        print(f" {task_id}: Data extraction successful")
                    else:
                        print(f"{task_id}: Data extraction failed or incomplete")
                else:
                    print(f"{task_id}: Not on expected drilldown page. URL: {current_url}")
                    extracted_data = {
                        'extraction_data': {
                            'success': False,
                            'error': f'Unexpected page URL: {current_url}',
                            'expected_url_pattern': 'AnalyzeData?chartId=drillDown'
                        }
                    }
                    extraction_success = False
            else:
                print(f" {task_id}: Skipping data extraction due to navigation failure")
                extracted_data = {
                    'extraction_data': {
                        'success': False,
                        'error': 'Navigation failed, cannot extract data',
                        'navigation_error': navigation_result.get('error', 'Unknown navigation error') if navigation_result else 'No navigation result'
                    }
                }
                extraction_success = False
            # Determine overall success
            overall_success = (
                click_result.get('success', False) and
                navigation_result.get('success', False) and
                extraction_success
            )
            result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'dataset_label': dataset_label,
                'click_result': click_result,
                'navigation_result': navigation_result,
                'extracted_data': extracted_data,
                'timestamp': datetime.now().isoformat(),
                'success': overall_success,
                'legend_controlled': legend_enabled,
                'drilldown_url': navigation_result.get('url', '') if navigation_result else '',
                'click_success': click_result.get('success', False) if click_result else False,
                'navigation_success': navigation_result.get('success', False) if navigation_result else False,
                'extraction_success': extraction_success
            }
            print(f" {task_id}: Successfully processed {point_label} from {dataset_label}")
            return result
        except Exception as e:
            print(f" {task_id}: Error processing point: {e}")
            error_result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'legend_controlled': False
            }
            return error_result

    async def extract_data_from_drilldown_page(self, page, point_data, target_month_year):
        """Extract MUI Grid data from drilldown page focusing on h5 and h6 tags only"""
        max_retries = 3
        retry_delay = 2  # seconds between retries
        
        for attempt in range(max_retries):
            try:
                print(f"Extracting drill-down page data... (Attempt {attempt + 1}/{max_retries})")

                # Wait for page to load completely
                # await page.wait_for_load_state("networkidle", timeout=10000)
                await asyncio.sleep(3)
                extraction_data = {
                    "extraction_timestamp": datetime.now().isoformat(),
                    "page_url": page.url,
                    "mui_grid_data": [],
                    "all_text_content": [],
                    "raw_html_sections": [],
                    "monetary_data": [],
                    "success": False,
                    "error": None,
                    "attempt": attempt + 1
                }
                # Method 1: Look for MUI Grid containers with the specific structure
                mui_grid_selectors = [
                    '.MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3',
                    '.MuiGrid-root.MuiGrid-container',
                    '[class*="MuiGrid-container"]'
                ]
                for selector in mui_grid_selectors:
                    try:
                        grid_containers = await page.query_selector_all(selector)
                        print(f"Found {len(grid_containers)} grid containers with selector: {selector}")
                        for container_index, container in enumerate(grid_containers):
                            if await container.is_visible():
                                # Extract the specific structure we're looking for
                                grid_items = await container.query_selector_all('.MuiGrid-item')
                                print(f"Container {container_index} has {len(grid_items)} grid items")
                                container_data = {
                                    "container_index": container_index,
                                    "selector_used": selector,
                                    "items": []
                                }
                                for item_index, item in enumerate(grid_items):
                                    # Look for h5 (title) and h6 (value) elements
                                    h5_element = await item.query_selector('h5.MuiTypography-root.MuiTypography-h5')
                                    h6_element = await item.query_selector('h6.MuiTypography-root.MuiTypography-subtitle1')
                                    if h5_element and h6_element:
                                        title = (await h5_element.text_content()).strip()
                                        value = (await h6_element.text_content()).strip()
                                        item_data = {
                                            "item_index": item_index,
                                            "title": title,
                                            "value": value,
                                            "html_structure": {
                                                "h5_html": await h5_element.inner_html(),
                                                "h6_html": await h6_element.inner_html()
                                            }
                                        }
                                        container_data["items"].append(item_data)
                                        # print(f"Extracted: {title} - {value}")
                                if container_data["items"]:
                                    extraction_data["mui_grid_data"].append(container_data)
                                    # print(f"Added container {container_index} with {len(container_data['items'])} items")

                    except Exception as selector_error:
                        print(f"Error with selector {selector}: {selector_error}")
                        continue
                # Determine success
                extraction_data["success"] = len(extraction_data["mui_grid_data"]) > 0
                print(f"Extraction success: {extraction_data['success']}")
                print(f"MUI Grid data items: {len(extraction_data['mui_grid_data'])}")
                # If successful, return the result immediately
                if extraction_data["success"]:
                    print(f" Data extraction successful on attempt {attempt + 1}")
                    result = {
                        'target_month_year': target_month_year,
                        'point_data': point_data,
                        'click_success': True,
                        'navigation_success': True,
                        'extraction_data': extraction_data,
                        'error': None,
                        'processing_time': 0,
                        'screenshot_path': None
                    }
                    return result

                # If not successful and not the last attempt, wait and retry
                if attempt < max_retries - 1:
                    print(f"Attempt {attempt + 1} failed, retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)                   
                else:
                    print(f" All {max_retries} attempts failed")
                    # Return failure result after all attempts exhausted
                    result = {
                        'target_month_year': target_month_year,
                        'point_data': point_data,
                        'click_success': True,
                        'navigation_success': True,
                        'extraction_data': extraction_data,
                        'error': f"Data extraction failed after {max_retries} attempts",
                        'processing_time': 0,
                        'screenshot_path': None
                    }
                    return result

            except Exception as e:
                print(f" Error extracting drill-down page data on attempt {attempt + 1}: {e}")
              
                # If not the last attempt, wait and retry
                if attempt < max_retries - 1:
                    print(f"Attempt {attempt + 1} failed with error, retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)
                else:
                    print(f" All {max_retries} attempts failed with errors")
                    # Return error result after all attempts exhausted
                    return {
                        'target_month_year': target_month_year,
                        'point_data': point_data,
                        'click_success': True,
                        'navigation_success': True,
                        'extraction_data': {
                            "extraction_timestamp": datetime.now().isoformat(),
                            "success": False,
                            "error": str(e),
                            "attempt": attempt + 1
                        },
                        'error': str(e),
                        'processing_time': 0,
                        'screenshot_path': None
                    }
        return {
            'target_month_year': target_month_year,
            'point_data': point_data,
            'click_success': True,
            'navigation_success': True,
            'extraction_data': {
                "extraction_timestamp": datetime.now().isoformat(),
                "success": False,
                "error": "Maximum retries exceeded",
                "attempt": max_retries
            },
            'error': "Maximum retries exceeded",
            'processing_time': 0,
            'screenshot_path': None
        }
    async def process_all_combinations_with_selective_legend_control(self, combinations):
        """Process all combinations with selective legend control and drilldown"""     
        # Organize combinations by chart
        chart_combinations = {}
        for combo_idx, combination in enumerate(combinations):
            chart_id = combination.get('chart_id', f'chart_{combo_idx}')
            target_month = combination.get('target_month_year', 'unknown')
            matching_points = combination.get('matching_points', [])

            chart_combinations[chart_id] = {
                'combination_index': combo_idx,
                'chart_info': combination['chart_info'],
                'target_month_year': target_month,
                'matching_points': matching_points,
                'current_point_index': 0
            }
            print(f"📋 Chart {chart_id}: {target_month} ({len(matching_points)} points)")

        max_points = max(len(chart['matching_points']) for chart in chart_combinations.values()) if chart_combinations else 0
        print(f"🎯 Maximum points in any chart: {max_points}")
        all_results = []
        target_month_year = TARGET_MONTHS_YEARS
        # Process points round by round with parallel processing
        for point_round in range(max_points):
            print(f"\nProcessing round {point_round + 1}/{max_points}")
            # Create tasks for this round
            round_tasks = []
            task_counter = 0
            for chart_id, chart_data in chart_combinations.items():
                matching_points = chart_data['matching_points']
                if point_round < len(matching_points):
                    point = matching_points[point_round]
                    task = {
                        'task_id': f"round_{point_round}_chart_{chart_id}",
                        'chart_id': chart_id,
                        'chart_info': chart_data['chart_info'],
                        'target_month_year': chart_data['target_month_year'],
                        'point_data': point,
                        'point_index': point_round,
                        'browser_id': f"browser_{task_counter % MAX_CONCURRENT_BROWSERS}"
                    }
                    round_tasks.append(task)
                    task_counter += 1
            if not round_tasks:
                continue
            # Group tasks by browser for parallel processing
            browser_task_groups = {}
            for i in range(MAX_CONCURRENT_BROWSERS):
                browser_task_groups[f'browser_{i}'] = []
            for task in round_tasks:
                browser_id = task['browser_id']
                browser_task_groups[browser_id].append(task)            
            # Process browser groups in parallel with enhanced legend control
            async def process_browser_round_tasks_with_selective_legend(browser_id, tasks):
                """Process tasks for a specific browser with selective legend control and parallel processing"""
                print(f"🚀 {browser_id}: Starting round {point_round + 1} with {len(tasks)} tasks")
                browser_results = []
                async with async_playwright() as playwright:
                    browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)
                    try:
                        # Navigate to SpecialMetrics
                        await page.goto("https://sampackag.fixedops.cc/SpecialMetrics", timeout=30000)
                        # await page.wait_for_load_state("networkidle", timeout=15000)
                        await asyncio.sleep(2)
                        # Apply enhanced legend control
                        legend_setup_success = await self.apply_enhanced_legend_control(page)
                        await asyncio.sleep(2)
                        if not legend_setup_success:
                            print(f"{browser_id}: Legend control setup failed, attempting manual setup...")
                            await self.debug_and_setup_charts(page)
                        # Disable all legends initially
                        await self.disable_all_legends(page)
                        await asyncio.sleep(1)
                        print(f" {browser_id}: Legend control applied, ready for selective processing")
                    except Exception as e:
                        print(f" {browser_id}: Navigation error: {str(e)}")
                        return browser_results
                    # Process each task with selective legend control
                    for task_idx, task in enumerate(tasks):
                        chart_id = task['chart_id']
                        point_label = task['point_data'].get('xLabel', 'Unknown')
                        dataset_label = task['point_data'].get('datasetLabel', 'Unknown Dataset')
                        print(f"{browser_id}: Processing task {task_idx + 1}/{len(tasks)} - {chart_id} - {point_label} ({dataset_label})")
                        try:
                            # Step 1: Disable ALL legends first
                            print(f"🔒 {browser_id}: Disabling all legends before processing {chart_id}")
                            await self.disable_all_legends(page)
                            await asyncio.sleep(1)
                            # Step 2: Enable ONLY the legend for the current chart being processed
                            print(f"🔓 {browser_id}: Enabling ONLY legend for {chart_id} - {dataset_label}")
                            legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
                            await asyncio.sleep(1)
                            if legend_enabled:
                                print(f" {browser_id}: Legend control successful - ONLY {chart_id} legend is active")
                            else:
                                print(f"{browser_id}: Legend control failed, but continuing with processing")

                            # Step 3: Process with selective legend control
                            result = await self.process_single_point_task_with_selective_legend(page, task, target_month_year)
                            if isinstance(result, dict):
                                result['browser_id'] = browser_id
                                result['round'] = point_round + 1
                                result['method'] = 'selective_legend_control'
                                result['task_sequence'] = task_idx + 1
                            browser_results.append(result)
                            print(f" {browser_id}: Completed task {task_idx + 1} - {chart_id} - {point_label}")
                            # Step 4: Disable ALL legends after processing and navigate back if more tasks
                            if task_idx < len(tasks) - 1:  # Not the last task
                                print(f"🔒 {browser_id}: Task completed - Disabling ALL legends before next task")
                                await self.disable_all_legends(page)
                                await page.goto("https://sampackag.fixedops.cc/SpecialMetrics", timeout=30000)
                                # await page.wait_for_load_state("networkidle", timeout=15000)
                                await asyncio.sleep(2)
                                # Re-apply legend control for next task
                                print(f"{browser_id}: Re-applying legend control for next task")
                                await self.apply_enhanced_legend_control(page)
                                await asyncio.sleep(1)
                            else:
                                print(f"🏁 {browser_id}: Final task completed - All legends will remain disabled")
                        except Exception as e:
                            print(f" {browser_id}: Error processing task {task_idx + 1} - {chart_id} - {point_label}: {str(e)}")
                            error_result = {
                                'task_id': task['task_id'],
                                'browser_id': browser_id,
                                'round': point_round + 1,
                                'error': str(e),
                                'chart_id': chart_id,
                                'point_label': point_label,
                                'status': 'failed',
                                'method': 'selective_legend_control',
                                'task_sequence': task_idx + 1
                            }
                            browser_results.append(error_result)
                    print(f" {browser_id}: Completed round {point_round + 1} - {len(browser_results)} results")
                    try:
                        await context.close()
                        await browser.close()
                    except Exception as cleanup_error:
                        print(f"{browser_id}: Cleanup error: {cleanup_error}")
                return browser_results            
            # Run browser groups in parallel
            browser_tasks = [
                asyncio.create_task(process_browser_round_tasks_with_selective_legend(browser_id, tasks))
                for browser_id, tasks in browser_task_groups.items()
                if tasks
            ]            
            round_results_list = await asyncio.gather(*browser_tasks, return_exceptions=True)
       
            # Collect results from this round
            round_results = []
            for browser_results in round_results_list:
                if isinstance(browser_results, Exception):
                    print(f"Browser processing failed in round {point_round + 1}: {str(browser_results)}")
                    continue
                round_results.extend(browser_results)            
            all_results.extend(round_results)
            print(f" Round {point_round + 1} completed: {len(round_results)} results")            
            # Add delay between rounds
            if point_round < max_points - 1:
                await asyncio.sleep(2.0)        
        # Process final results
        successful_results = []
        failed_results = []        
        for result in all_results:
            if isinstance(result, dict) and result.get('success', False):
                successful_results.append(result)
            else:
                failed_results.append(result)
        
        print(f"\n🎉 Processing completed with selective legend control!")
        print(f"Summary:")
        print(f"   - Total rounds processed: {max_points}")
        print(f"   - Total charts: {len(chart_combinations)}")
        print(f"   - Total tasks processed: {len(all_results)}")
        print(f"   - Successful: {len(successful_results)}")
        print(f"   - Failed: {len(failed_results)}")        
        return {
            'successful': successful_results,
            'failed': failed_results,
            'all_results': all_results,
            'total_processed': len(all_results),
            'total_charts': len(chart_combinations),
            'rounds_processed': max_points
        }
    async def enable_legend_for_chart(self, page, chart_id):
        """Enable legend for a specific chart"""
        try:
            await page.evaluate(f"""
                if (window.chartInstances && window.chartInstances.has('{chart_id}')) {{
                    const chartData = window.chartInstances.get('{chart_id}');
                    const chart = chartData.instance;
                    
                    // Enable legend
                    if (!chart.options.plugins) chart.options.plugins = {{}};
                    if (!chart.options.plugins.legend) chart.options.plugins.legend = {{}};
                    chart.options.plugins.legend.display = true;
                    
                    // Update chart
                    chart.update('none');
                    
                    console.log(`Legend enabled for {chart_id}`);
                }}
            """)
            
            print(f" Legend enabled for {chart_id}")
            return True
            
        except Exception as e:
            print(f" Failed to enable legend for {chart_id}: {str(e)}")
            return False
    async def disable_legend_for_chart(self, page, chart_id):
        """Disable legend for a specific chart"""
        try:
            await page.evaluate(f"""
                if (window.chartInstances && window.chartInstances.has('{chart_id}')) {{
                    const chartData = window.chartInstances.get('{chart_id}');
                    const chart = chartData.instance;
                    
                    // Disable legend
                    if (!chart.options.plugins) chart.options.plugins = {{}};
                    if (!chart.options.plugins.legend) chart.options.plugins.legend = {{}};
                    chart.options.plugins.legend.display = false;
                    
                    // Update chart
                    chart.update('none');
                    
                    console.log(`Legend disabled for {chart_id}`);
                }}
            """)
            
            print(f" Legend disabled for {chart_id}")
            return True
            
        except Exception as e:
            print(f" Failed to disable legend for {chart_id}: {str(e)}")
            return False

    async def debug_and_setup_charts(self, page):
        """Debug and manually setup chart instances"""
        try:
            result = await page.evaluate("""
                (function() {
                    console.log('=== CHART DEBUG AND SETUP ===');

                    // Check if Chart.js is available
                    console.log('Chart.js available:', typeof Chart !== 'undefined');

                    // Find all canvas elements
                    const allCanvases = document.querySelectorAll('canvas');
                    console.log(`Total canvas elements found: ${allCanvases.length}`);

                    const chartCanvases = document.querySelectorAll('canvas.chartjs-render-monitor');
                    console.log(`Chart.js canvas elements found: ${chartCanvases.length}`);

                    // Initialize chart instances map
                    if (!window.chartInstances) {
                        window.chartInstances = new Map();
                    }

                    let foundCharts = 0;

                    // Try multiple methods to find charts
                    chartCanvases.forEach((canvas, index) => {
                        let chartInstance = null;

                        console.log(`Processing canvas ${index}:`);

                        // Method 1: Chart.getChart
                        if (typeof Chart !== 'undefined' && Chart.getChart) {
                            try {
                                chartInstance = Chart.getChart(canvas);
                                if (chartInstance) {
                                    console.log(`  - Found via Chart.getChart`);
                                }
                            } catch (e) {
                                console.log(`  - Chart.getChart failed:`, e.message);
                            }
                        }

                        // Method 2: Canvas chart property
                        if (!chartInstance && canvas.chart) {
                            chartInstance = canvas.chart;
                            console.log(`  - Found via canvas.chart`);
                        }

                        // Method 3: Canvas _chart property
                        if (!chartInstance && canvas._chart) {
                            chartInstance = canvas._chart;
                            console.log(`  - Found via canvas._chart`);
                        }

                        // Method 4: Search Chart.instances
                        if (!chartInstance && typeof Chart !== 'undefined' && Chart.instances) {
                            Object.values(Chart.instances).forEach(instance => {
                                if (instance.canvas === canvas) {
                                    chartInstance = instance;
                                    console.log(`  - Found via Chart.instances`);
                                }
                            });
                        }

                        if (chartInstance && chartInstance.data && chartInstance.data.datasets) {
                            const chartId = `chart_${index}`;
                            window.chartInstances.set(chartId, {
                                instance: chartInstance,
                                canvas: canvas,
                                originalLegendDisplay: chartInstance.options?.plugins?.legend?.display ?? true
                            });

                            console.log(`  - Registered as ${chartId} with ${chartInstance.data.datasets.length} datasets`);
                            chartInstance.data.datasets.forEach((dataset, dsIndex) => {
                                console.log(`    Dataset ${dsIndex}: ${dataset.label || 'Unnamed'}`);
                            });

                            foundCharts++;
                        } else {
                            console.log(`  - No valid chart instance found`);
                        }
                    });

                    console.log(`=== SETUP COMPLETE: ${foundCharts} charts registered ===`);
                    return foundCharts;
                })()
            """)

            print(f"Debug setup completed: {result} charts found and registered")
            return result > 0

        except Exception as e:
            print(f" Debug setup failed: {str(e)}")
            return False

    async def click_legend_line(self, page, chart_id, dataset_label):
        """Click on a specific legend line to enable/highlight the dataset"""
        try:
            # First, try to find and click the legend item
            legend_clicked = await page.evaluate(f"""
            (function() {{
                if (window.chartInstances && window.chartInstances.has('{chart_id}')) {{
                    const chartData = window.chartInstances.get('{chart_id}');
                    const chart = chartData.instance;
                    // Find the dataset index by label
                    let datasetIndex = -1;
                    chart.data.datasets.forEach((dataset, index) => {{
                        if (dataset.label === '{dataset_label}') {{
                            datasetIndex = index;
                        }}
                    }});
                    if (datasetIndex >= 0) {{
                        // Hide all datasets except the target one
                        chart.data.datasets.forEach((dataset, index) => {{
                            const meta = chart.getDatasetMeta(index);
                            if (meta) {{
                                meta.hidden = (index !== datasetIndex);
                            }}
                        }});
                        // Update chart
                        chart.update('none');
                        console.log('Activated dataset: {dataset_label} (index: ' + datasetIndex + ')');
                        return true;
                    }}
                    console.log('Dataset not found: {dataset_label}');
                    return false;
                }}
                return false;
            }})()
            """)
            
            if legend_clicked:
                print(f" Legend line clicked for {chart_id} - {dataset_label}")
                return True
            else:
                print(f"Could not click legend line for {chart_id} - {dataset_label}")
                return False
        except Exception as e:
            print(f" Failed to click legend line for {chart_id} - {dataset_label}: {str(e)}")
            return False
    
    async def process_single_point_task_with_legend_control(self, page, task, target_month_year):
        """Process a single point task with legend control - modified version"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        chart_id = task['chart_id']
        point_data = task['point_data']
        
        try:
            # Click on the specific data point and extract data
            point_label = point_data.get('xLabel', 'Unknown')
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            
            print(f"🎯 {task_id}: Clicking point {point_label} from {dataset_label}")
            
            # Wait a moment to ensure legend control is applied
            await asyncio.sleep(1)
            
            extracted_data = await self.click_and_extract_data(page, point_data, target_month_year)
            
            result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'dataset_label': dataset_label,
                'extracted_data': extracted_data,
                'timestamp': datetime.now().isoformat(),
                'success': extracted_data.get('click_success', False) if extracted_data else False,
                'legend_controlled': True
            }
            
            print(f" {task_id}: Successfully processed {point_label} from {dataset_label}")
            return result
            
        except Exception as e:
            print(f" {task_id}: Error processing point: {e}")
            
            error_result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'legend_controlled': False
            }
            return error_result
    async def click_chart_data_point(self, page, chart_id, point_data):
        """Click on a specific chart data point directly"""
        try:
            # Extract point information
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            x_label = point_data.get('xLabel', 'Unknown')
            dataset_index = point_data.get('datasetIndex', 0)
            point_index = point_data.get('pointIndex', 0)
            
            # Click on the specific data point
            point_clicked = await page.evaluate(f"""
            (function() {{
                if (window.chartInstances && window.chartInstances.has('{chart_id}')) {{
                    const chartData = window.chartInstances.get('{chart_id}');
                    const chart = chartData.instance;
                    
                    try {{
                        // Find the dataset by label if datasetIndex is not reliable
                        let targetDatasetIndex = {dataset_index};
                        
                        // Verify dataset index by label
                        chart.data.datasets.forEach((dataset, index) => {{
                            if (dataset.label === '{dataset_label}') {{
                                targetDatasetIndex = index;
                            }}
                        }});
                        
                        // Get the dataset meta
                        const meta = chart.getDatasetMeta(targetDatasetIndex);
                        if (!meta || !meta.data || !meta.data[{point_index}]) {{
                            console.log('Data point not found at index: ' + {point_index});
                            return false;
                        }}
                        
                        // Get the data point element
                        const pointElement = meta.data[{point_index}];
                        if (!pointElement) {{
                            console.log('Point element not found');
                            return false;
                        }}
                        
                        // Get the chart canvas
                        const canvas = chart.canvas;
                        if (!canvas) {{
                            console.log('Canvas not found');
                            return false;
                        }}
                        
                        // Get point position
                        const pointPosition = pointElement.getCenterPoint();
                        
                        // Create and dispatch click event
                        const rect = canvas.getBoundingClientRect();
                        const clickEvent = new MouseEvent('click', {{
                            clientX: rect.left + pointPosition.x,
                            clientY: rect.top + pointPosition.y,
                            bubbles: true,
                            cancelable: true
                        }});
                        
                        // Dispatch the click event
                        canvas.dispatchEvent(clickEvent);
                        
                        console.log('Clicked data point: ' + '{x_label}' + ' from dataset: ' + '{dataset_label}');
                        console.log('Point position:', pointPosition);
                        
                        return true;
                        
                    }} catch (error) {{
                        console.error('Error clicking data point:', error);
                        return false;
                    }}
                }}
                console.log('Chart instance not found: {chart_id}');
                return false;
            }})()
            """)
            
            if point_clicked:
                print(f" Data point clicked for {chart_id} - {x_label} from {dataset_label}")
                return True
            else:
                print(f"Could not click data point for {chart_id} - {x_label} from {dataset_label}")
                return False
                
        except Exception as e:
            print(f" Failed to click data point for {chart_id} - {x_label}: {str(e)}")
            return False

    async def process_single_point_task_with_direct_clicking(self, page, task, target_month_year):
        """Process a single point task with direct data point clicking"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        chart_id = task['chart_id']
        point_data = task['point_data']
        
        try:
            # Click on the specific data point directly
            point_label = point_data.get('xLabel', 'Unknown')
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            
            print(f"🎯 {task_id}: Clicking data point {point_label} from {dataset_label}")
            
            # Click directly on the chart data point
            click_success = await self.click_chart_data_point(page, chart_id, point_data)
            
            if not click_success:
                print(f"{task_id}: Failed to click data point, attempting fallback method")
                # Fallback to the original click_and_extract_data method
                extracted_data = await self.click_and_extract_data(page, point_data, target_month_year)
            else:
                # Wait a moment after clicking
                await asyncio.sleep(1)
                # Extract data after clicking
                extracted_data = await self.click_and_extract_data(page, point_data, target_month_year)
            
            result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'dataset_label': dataset_label,
                'extracted_data': extracted_data,
                'timestamp': datetime.now().isoformat(),
                'success': extracted_data.get('click_success', False) if extracted_data else False,
                'direct_click_success': click_success
            }
            
            print(f" {task_id}: Successfully processed {point_label} from {dataset_label}")
            return result
            
        except Exception as e:
            print(f" {task_id}: Error processing point: {e}")
            
            error_result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'direct_click_success': False
            }
            return error_result
    async def process_all_combinations_parallel(self, combinations):
        """Process charts in parallel with 3 browsers, each handling different charts"""

        print(f"🚀 Starting parallel processing of {len(combinations)} charts with 3 browsers")
        # print("combinations===========================",combinations)
        # Organize combinations by chart for easier processing
        chart_combinations = {}
        for combo_idx, combination in enumerate(combinations):
            chart_id = combination.get('chart_id', f'chart_{combo_idx}')
            target_month = combination.get('target_month_year', 'unknown')
            matching_points = combination.get('matching_points', [])

            chart_combinations[chart_id] = {
                'combination_index': combo_idx,
                'chart_info': combination['chart_info'],
                'target_month_year': target_month,
                'matching_points': matching_points,
                'current_point_index': 0
            }

            print(f"**Chart {chart_id}: {target_month} ({(len(matching_points))} points)")

        all_results = []
        target_month_year = TARGET_MONTHS_YEARS
        max_browsers = 3

        # Group charts for parallel processing (3 charts at a time)
        chart_items = list(chart_combinations.items())
        chart_batches = []

        # Create batches of 3 charts each
        for i in range(0, len(chart_items), max_browsers):
            batch = chart_items[i:i + max_browsers]
            chart_batches.append(batch)

        print(f"Processing {len(chart_batches)} batches with up to {max_browsers} browsers per batch")

        # Process each batch of charts in parallel
        for batch_index, chart_batch in enumerate(chart_batches, 1):
            print(f"\n🚀 Starting Batch {batch_index}/{len(chart_batches)} with {len(chart_batch)} charts")

            # Create parallel tasks for this batch
            batch_tasks = []
            for browser_index, (chart_id, chart_data) in enumerate(chart_batch):
                browser_id = f"Browser_{browser_index + 1}"
                print(f"   📋 {browser_id}: Will process Chart {chart_id} - {chart_data['chart_info'].get('chartTitle', 'Unknown')}")
                print(chart_data,"chart_data+++++++++++++++")
                task = asyncio.create_task(
                    self.process_single_chart_parallel(chart_data, target_month_year, browser_id,chart_id)
                )
                batch_tasks.append((browser_id, chart_id, task))

            # Wait for all browsers in this batch to complete
            print(f"Waiting for all {len(batch_tasks)} browsers in batch {batch_index} to complete...")

            for browser_id, chart_id, task in batch_tasks:
                try:
                    result = await task
                    if isinstance(result, list):
                        all_results.extend(result)
                        print(f" {browser_id} completed Chart {chart_id}: {len(result)} results")
                    else:
                        print(f"{browser_id} returned unexpected result type for Chart {chart_id}")
                except Exception as e:
                    print(f" {browser_id} failed processing Chart {chart_id}: {str(e)}")
                    continue

            print(f" Batch {batch_index} completed")

            # Add delay between batches to avoid overwhelming the system
            if batch_index < len(chart_batches):
                print(f"Waiting before next batch...")
                await asyncio.sleep(3)

        # Process final results
        successful_results = []
        failed_results = []

        for result in all_results:
            if isinstance(result, dict) and result.get('success', False):
                successful_results.append(result)
            else:
                failed_results.append(result)

        print(f"\n🎉 Parallel processing with 3 browsers completed!")
        print(f"Summary:")
        print(f"   - Total charts processed: {len(chart_combinations)}")
        print(f"   - Total batches processed: {len(chart_batches)}")
        print(f"   - Total point tasks processed: {len(all_results)}")
        print(f"   - Successful: {len(successful_results)}")
        print(f"   - Failed: {len(failed_results)}")
        print(f"   - Success rate: {(len(successful_results) / len(all_results) * 100):.1f}%" if all_results else "0%")
        print(all_results,"all_results+++++++++++")
        return {
            'successful': successful_results,
            'failed': failed_results,
            'all_results': all_results,
            'total_processed': len(all_results),
            'total_charts': len(chart_combinations),
            'batches_processed': len(chart_batches),
            'success_rate': (len(successful_results) / len(all_results) * 100) if all_results else 0
        }

    async def process_single_chart_parallel(self, chart_data, target_month_year, browser_id,chart_id):
        """Process all points in a single chart in parallel (for use with multiple browsers)"""
        
        chart_title = chart_data.get('chart_info', {}).get('chartTitle', 'Unknown Chart')
        matching_points = chart_data.get('matching_points', [])

        print(f"{browser_id}: Processing chart: {chart_title} :({chart_id}) with {len(matching_points)} points")

        chart_results = []

        async with async_playwright() as playwright:
            browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)

            try:
                # Navigate to SpecialMetrics
                print(f"{browser_id}: Navigating to SpecialMetrics for {chart_id}")
                await page.goto("https://sampackag.fixedops.cc/SpecialMetrics", timeout=30000)
                # await page.wait_for_load_state("networkidle", timeout=15000)
                await asyncio.sleep(2)

                # Apply enhanced legend control
                legend_setup_success = await self.apply_enhanced_legend_control(page)
                await asyncio.sleep(2)

                if not legend_setup_success:
                    print(f"{browser_id}: Legend control setup failed for {chart_id}, attempting manual setup...")
                    await self.debug_and_setup_charts(page)

                # Debug legend control setup
                await self.debug_legend_control(page)               

                # Process each point in this chart sequentially within this browser
                for point_idx, point_data in enumerate(matching_points):
                    point_label = point_data.get('xLabel', f'Point_{point_idx}')
                    dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')

                    # print(f"\n{browser_id}: Processing point {point_idx + 1}/{len(matching_points)}: {point_label} ({dataset_label})")

                    try:
                        # Step 1: Disable ALL legends first
                        # print(f"🔒 {browser_id}: Disabling all legends before processing {chart_id}")
                        await self.disable_all_legends(page)
                        await asyncio.sleep(1)

                        # Step 2: Enable ONLY the legend for current chart/dataset
                        # print(f"🔓 {browser_id}: Enabling ONLY legend for {chart_id} - {dataset_label}")
                        legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
                        await asyncio.sleep(2)  # Give more time for chart to update

                        if legend_enabled:
                            print(f" {browser_id}: Legend control successful - ONLY {chart_id} legend is active")
                        else:
                            print(f"{browser_id}: Legend control failed, but continuing with processing")

                        # Step 2.5: Ensure chart is interactive and data points are clickable
                        print(f"{browser_id}: Ensuring chart {chart_id} is interactive after legend control...")
                        await self.ensure_chart_interactivity(page, chart_id)
                        await asyncio.sleep(1)

                        # Step 3: Create task for this point
                        task = {
                            'task_id': f"{browser_id}_{chart_id}_point_{point_idx}",
                            'chart_id': chart_id,
                            'chart_info': chart_data['chart_info'],
                            'target_month_year': chart_data['target_month_year'],
                            'point_data': point_data,
                            'point_index': point_idx
                        }

                        # Step 4: Process this point with enhanced clicking
                        result = await self.process_single_point_task_with_enhanced_clicking(page, task, target_month_year)

                        if isinstance(result, dict):
                            result['chart_title'] = chart_title
                            result['point_sequence'] = point_idx + 1
                            result['method'] = 'parallel_processing'
                            result['browser_id'] = browser_id
                            result['chart_id'] = chart_id

                        chart_results.append(result)

                        # Log detailed result
                        if result.get('success', False):
                            click_success = result.get('click_success', False)
                            nav_success = result.get('navigation_success', False)
                            extract_success = result.get('extraction_success', False)
                            # print(f" {browser_id}: Completed point {point_idx + 1}: {point_label}")
                            # print(f"   Click: {'' if click_success else ''} | Navigation: {'' if nav_success else ''} | Extraction: {'' if extract_success else ''}")
                            if nav_success:
                                drilldown_url = result.get('drilldown_url', 'Unknown')
                                print(f"   🔗 Drilldown URL: {drilldown_url}")
                        else:
                            error_msg = result.get('error', 'Unknown error')
                            print(f" {browser_id}: Failed point {point_idx + 1}: {point_label} - {error_msg}")

                        # Step 5: Navigate back to SpecialMetrics for next point (if not last point)
                        if point_idx < len(matching_points) - 1:
                            print(f"{browser_id}: Navigating back to SpecialMetrics for next point")
                            try:
                                await page.goto("https://sampackag.fixedops.cc/SpecialMetrics", timeout=30000)
                                # await page.wait_for_load_state("networkidle", timeout=15000)
                                await asyncio.sleep(2)

                                # Re-apply legend control
                                await self.apply_enhanced_legend_control(page)
                                await asyncio.sleep(1)
                                print(f" {browser_id}: Successfully navigated back to SpecialMetrics")
                            except Exception as nav_back_error:
                                print(f" {browser_id}: Failed to navigate back to SpecialMetrics: {nav_back_error}")
                                # Try to continue anyway
                                pass

                    except Exception as e:
                        print(f" {browser_id}: Error processing point {point_idx + 1} - {point_label}: {str(e)}")
                        error_result = {
                            'task_id': f"{browser_id}_{chart_id}_point_{point_idx}",
                            'chart_id': chart_id,
                            'chart_title': chart_title,
                            'point_label': point_label,
                            'error': str(e),
                            'success': False,
                            'method': 'parallel_processing',
                            'browser_id': browser_id,
                            'point_sequence': point_idx + 1
                        }
                        chart_results.append(error_result)

                print(f" {browser_id}: Completed all points for chart: {chart_title}")

            except Exception as e:
                print(f" {browser_id}: Error setting up chart {chart_id}: {str(e)}")
                error_result = {
                    'chart_id': chart_id,
                    'chart_title': chart_title,
                    'error': f"Chart setup failed: {str(e)}",
                    'success': False,
                    'method': 'parallel_processing',
                    'browser_id': browser_id
                }
                chart_results.append(error_result)

            finally:
                try:
                    await context.close()
                    await browser.close()
                    print(f"🔒 {browser_id}: Browser closed for chart {chart_id}")
                except Exception as cleanup_error:
                    print(f"{browser_id}: Cleanup error for {chart_id}: {cleanup_error}")

        return chart_results

    async def process_single_chart_sequential(self, chart_data, target_month_year):
        """Process all points in a single chart sequentially"""
        chart_id = chart_data.get('chart_info', {}).get('canvasId', 'unknown_chart')
        chart_title = chart_data.get('chart_info', {}).get('chartTitle', 'Unknown Chart')
        matching_points = chart_data.get('matching_points', [])

        print(f"🎯 Processing chart: {chart_title} ({chart_id}) with {len(matching_points)} points")

        chart_results = []

        async with async_playwright() as playwright:
            browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)

            try:
                # Navigate to SpecialMetrics
                print(f"Navigating to SpecialMetrics for {chart_id}")
                await page.goto("https://sampackag.fixedops.cc/SpecialMetrics", timeout=30000)
                # await page.wait_for_load_state("networkidle", timeout=15000)
                await asyncio.sleep(2)

                # Apply enhanced legend control
                legend_setup_success = await self.apply_enhanced_legend_control(page)
                await asyncio.sleep(2)

                if not legend_setup_success:
                    print(f"Legend control setup failed for {chart_id}, attempting manual setup...")
                    await self.debug_and_setup_charts(page)

                # Debug legend control setup
                await self.debug_legend_control(page)

                print(f" Page setup completed for {chart_id}")

                # Process each point in this chart sequentially
                for point_idx, point_data in enumerate(matching_points):
                    point_label = point_data.get('xLabel', f'Point_{point_idx}')
                    dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')

                    print(f"\nProcessing point {point_idx + 1}/{len(matching_points)}: {point_label} ({dataset_label})")

                    try:
                        # Step 1: Disable ALL legends first
                        print(f"🔒 Disabling all legends before processing {chart_id}")
                        await self.disable_all_legends(page)
                        await asyncio.sleep(1)

                        # Step 2: Enable ONLY the legend for current chart/dataset
                        print(f"🔓 Enabling ONLY legend for {chart_id} - {dataset_label}")
                        legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
                        await asyncio.sleep(2)  # Give more time for chart to update

                        if legend_enabled:
                            print(f" Legend control successful - ONLY {chart_id} legend is active")
                        else:
                            print(f"Legend control failed, but continuing with processing")

                        # Step 2.5: Ensure chart is interactive and data points are clickable
                        print(f"Ensuring chart {chart_id} is interactive after legend control...")
                        await self.ensure_chart_interactivity(page, chart_id)
                        await asyncio.sleep(1)

                        # Step 3: Create task for this point
                        task = {
                            'task_id': f"{chart_id}_point_{point_idx}",
                            'chart_id': chart_id,
                            'chart_info': chart_data['chart_info'],
                            'target_month_year': chart_data['target_month_year'],
                            'point_data': point_data,
                            'point_index': point_idx
                        }

                        # Step 4: Process this point with enhanced clicking
                        result = await self.process_single_point_task_with_enhanced_clicking(page, task, target_month_year)

                        if isinstance(result, dict):
                            result['chart_title'] = chart_title
                            result['point_sequence'] = point_idx + 1
                            result['method'] = 'sequential_processing'

                        chart_results.append(result)

                        # Log detailed result
                        if result.get('success', False):
                            click_success = result.get('click_success', False)
                            nav_success = result.get('navigation_success', False)
                            extract_success = result.get('extraction_success', False)
                            print(f" Completed point {point_idx + 1}: {point_label}")
                            print(f"   Click: {'' if click_success else ''} | Navigation: {'' if nav_success else ''} | Extraction: {'' if extract_success else ''}")
                            if nav_success:
                                drilldown_url = result.get('drilldown_url', 'Unknown')
                                print(f"   🔗 Drilldown URL: {drilldown_url}")
                        else:
                            error_msg = result.get('error', 'Unknown error')
                            print(f" Failed point {point_idx + 1}: {point_label} - {error_msg}")

                        # Step 5: Navigate back to SpecialMetrics for next point (if not last point)
                        if point_idx < len(matching_points) - 1:
                            print(f"Navigating back to SpecialMetrics for next point")
                            try:
                                await page.goto("https://sampackag.fixedops.cc/SpecialMetrics", timeout=30000)
                                # await page.wait_for_load_state("networkidle", timeout=15000)
                                await asyncio.sleep(2)

                                # Re-apply legend control
                                await self.apply_enhanced_legend_control(page)
                                await asyncio.sleep(1)
                                print(f" Successfully navigated back to SpecialMetrics")
                            except Exception as nav_back_error:
                                print(f" Failed to navigate back to SpecialMetrics: {nav_back_error}")
                                # Try to continue anyway
                                pass

                    except Exception as e:
                        print(f" Error processing point {point_idx + 1} - {point_label}: {str(e)}")
                        error_result = {
                            'task_id': f"{chart_id}_point_{point_idx}",
                            'chart_id': chart_id,
                            'chart_title': chart_title,
                            'point_label': point_label,
                            'error': str(e),
                            'success': False,
                            'method': 'sequential_processing',
                            'point_sequence': point_idx + 1
                        }
                        chart_results.append(error_result)

                print(f" Completed all points for chart: {chart_title}")

            except Exception as e:
                print(f" Error setting up chart {chart_id}: {str(e)}")
                error_result = {
                    'chart_id': chart_id,
                    'chart_title': chart_title,
                    'error': f"Chart setup failed: {str(e)}",
                    'success': False,
                    'method': 'sequential_processing'
                }
                chart_results.append(error_result)

            finally:
                try:
                    await context.close()
                    await browser.close()
                except Exception as cleanup_error:
                    print(f"Cleanup error for {chart_id}: {cleanup_error}")

        return chart_results

    async def ensure_chart_interactivity(self, page, chart_id):
        """Ensure chart is interactive and data points are clickable after legend control"""
        try:
            result = await page.evaluate(f"""
            (function() {{
                console.log('Ensuring chart interactivity for: {chart_id}');

                if (!window.chartInstances) {{
                    console.log('No chart instances found');
                    return false;
                }}

                // Try multiple chart ID variations
                const chartIdVariations = ['{chart_id}', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;

                for (const id of chartIdVariations) {{
                    if (window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        break;
                    }}
                }}

                if (!chartData) {{
                    console.log('Chart not found for interactivity check');
                    return false;
                }}

                const chart = chartData.instance;

                try {{
                    // Ensure chart is responsive to events
                    if (chart.options) {{
                        if (!chart.options.interaction) chart.options.interaction = {{}};
                        chart.options.interaction.intersect = false;
                        chart.options.interaction.mode = 'point';

                        if (!chart.options.onHover) {{
                            chart.options.onHover = function(event, elements) {{
                                console.log('Chart hover detected:', elements.length, 'elements');
                            }};
                        }}

                        if (!chart.options.onClick) {{
                            chart.options.onClick = function(event, elements) {{
                                console.log('Chart click detected:', elements.length, 'elements');
                                if (elements.length > 0) {{
                                    const element = elements[0];
                                    console.log('Clicked element:', element);
                                }}
                            }};
                        }}
                    }}

                    // Force chart update to apply interaction settings
                    chart.update('none');

                    console.log('Chart interactivity ensured for: {chart_id}');
                    return true;
                }} catch (error) {{
                    console.error('Error ensuring chart interactivity:', error);
                    return false;
                }}
            }})()
            """)

            if result:
                print(f" Chart {chart_id} interactivity ensured")
            else:
                print(f"Failed to ensure chart {chart_id} interactivity")

            return result

        except Exception as e:
            print(f" Error ensuring chart interactivity: {str(e)}")
            return False

    async def process_single_point_task_with_enhanced_clicking(self, page, task, target_month_year):
        """Process a single point task with enhanced clicking methods for drilldown"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        chart_id = task['chart_id']
        point_data = task['point_data']

        try:
            point_label = point_data.get('xLabel', 'Unknown')
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')

            print(f"🎯 {task_id}: Processing {point_label} from {dataset_label} with enhanced clicking")

            # Get current URL before clicking
            initial_url = page.url
            print(f"{task_id}: Current URL before click: {initial_url}")

            # Method 1: Try Chart.js event-based clicking first
            click_result = await self.try_chartjs_event_click(page, chart_id, point_data)
            navigation_result = None

            if click_result.get('success', False):
                print(f" {task_id}: Chart.js event click successful, checking for navigation...")

                # Wait for navigation
                await asyncio.sleep(3)
                current_url = page.url

                if current_url != initial_url:
                    print(f" {task_id}: Navigation successful to: {current_url}")
                    navigation_result = {'success': True, 'url': current_url, 'method': 'chartjs_event'}
                else:
                    print(f"{task_id}: Chart.js event click didn't trigger navigation")
                    click_result = {'success': False, 'error': 'No navigation after Chart.js event click'}

            # Method 2: Fallback to coordinate clicking if Chart.js event didn't work
            if not click_result or not click_result.get('success', False):
                print(f"{task_id}: Trying coordinate-based clicking...")

                screen_x = point_data.get('screenX')
                screen_y = point_data.get('screenY')

                if screen_x and screen_y:
                    # Try multiple click methods
                    for click_method in ['single', 'double', 'with_delay']:
                        print(f"{task_id}: Trying {click_method} click at ({screen_x}, {screen_y})")

                        if click_method == 'single':
                            await page.mouse.click(screen_x, screen_y)
                        elif click_method == 'double':
                            await page.mouse.click(screen_x, screen_y, click_count=2)
                        elif click_method == 'with_delay':
                            await page.mouse.move(screen_x, screen_y)
                            await asyncio.sleep(0.5)
                            await page.mouse.down()
                            await asyncio.sleep(0.1)
                            await page.mouse.up()

                        # Check for navigation after each method
                        await asyncio.sleep(3)
                        current_url = page.url

                        if current_url != initial_url:
                            print(f" {task_id}: {click_method} click triggered navigation to: {current_url}")
                            click_result = {'success': True, 'method': f'coordinate_{click_method}'}
                            navigation_result = {'success': True, 'url': current_url, 'method': f'coordinate_{click_method}'}
                            break
                        else:
                            print(f"{task_id}: {click_method} click didn't trigger navigation")

                    if not navigation_result or not navigation_result.get('success', False):
                        click_result = {'success': False, 'error': 'All coordinate click methods failed'}
                        navigation_result = {'success': False, 'error': 'No navigation with any click method'}
                else:
                    click_result = {'success': False, 'error': 'No coordinates available'}
                    navigation_result = {'success': False, 'error': 'No coordinates for clicking'}

            # Continue with data extraction if navigation was successful
            if navigation_result and navigation_result.get('success', False):
                current_url = page.url
                print(f"{task_id}: Attempting data extraction from: {current_url}")

                if "AnalyzeData" in current_url and "chartId=drillDown" in current_url:
                    print(f" {task_id}: On correct drilldown page, extracting data...")
                    extracted_data = await self.extract_data_from_drilldown_page(page, point_data, target_month_year)

                    extraction_success = False
                    if extracted_data and extracted_data.get('extraction_data'):
                        extraction_success = extracted_data['extraction_data'].get('success', False)

                    if extraction_success:
                        print(f" {task_id}: Data extraction successful")
                    else:
                        print(f"{task_id}: Data extraction failed or incomplete")
                else:
                    print(f"{task_id}: Not on expected drilldown page. URL: {current_url}")
                    extracted_data = {
                        'extraction_data': {
                            'success': False,
                            'error': f'Unexpected page URL: {current_url}',
                            'expected_url_pattern': 'AnalyzeData?chartId=drillDown'
                        }
                    }
                    extraction_success = False
            else:
                print(f" {task_id}: Skipping data extraction due to navigation failure")
                extracted_data = {
                    'extraction_data': {
                        'success': False,
                        'error': 'Navigation failed, cannot extract data',
                        'navigation_error': navigation_result.get('error', 'Unknown navigation error') if navigation_result else 'No navigation result'
                    }
                }
                extraction_success = False
            # Determine overall success
            overall_success = (
                click_result.get('success', False) and
                navigation_result.get('success', False) and
                extraction_success
            )
            result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'dataset_label': dataset_label,
                'click_result': click_result,
                'navigation_result': navigation_result,
                'extracted_data': extracted_data,
                'timestamp': datetime.now().isoformat(),
                'success': overall_success,
                'legend_controlled': True,  # We know legend control was attempted
                'drilldown_url': navigation_result.get('url', '') if navigation_result else '',
                'click_success': click_result.get('success', False) if click_result else False,
                'navigation_success': navigation_result.get('success', False) if navigation_result else False,
                'extraction_success': extraction_success
            }
            print(f" {task_id}: Enhanced processing completed for {point_label} from {dataset_label}")
            return result

        except Exception as e:
            print(f" {task_id}: Error in enhanced processing: {e}")
            error_result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'legend_controlled': True
            }
            return error_result

    async def try_chartjs_event_click(self, page, chart_id, point_data):
        """Try to trigger Chart.js click event programmatically"""
        try:
            result = await page.evaluate(f"""
            (function() {{
                console.log('Attempting Chart.js event click for chart: {chart_id}');

                if (!window.chartInstances) {{
                    console.log('No chart instances found');
                    return {{ success: false, error: 'No chart instances' }};
                }}

                // Try multiple chart ID variations
                const chartIdVariations = ['{chart_id}', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;

                for (const id of chartIdVariations) {{
                    if (window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        break;
                    }}
                }}

                if (!chartData) {{
                    console.log('Chart not found for event click');
                    return {{ success: false, error: 'Chart not found' }};
                }}

                const chart = chartData.instance;
                const canvas = chartData.canvas;

                try {{
                    // Get point data
                    const pointIndex = {point_data.get('pointIndex', 0)};
                    const datasetIndex = {point_data.get('datasetIndex', 0)};

                    // Get the data point element
                    const meta = chart.getDatasetMeta(datasetIndex);
                    if (!meta || !meta.data || !meta.data[pointIndex]) {{
                        console.log('Data point not found');
                        return {{ success: false, error: 'Data point not found' }};
                    }}

                    const pointElement = meta.data[pointIndex];
                    const pointPosition = pointElement.getCenterPoint();

                    // Create a synthetic click event
                    const rect = canvas.getBoundingClientRect();
                    const clickEvent = new MouseEvent('click', {{
                        clientX: rect.left + pointPosition.x,
                        clientY: rect.top + pointPosition.y,
                        bubbles: true,
                        cancelable: true,
                        view: window
                    }});

                    // Trigger the click event
                    canvas.dispatchEvent(clickEvent);

                    // Also try Chart.js onClick if available
                    if (chart.options && chart.options.onClick) {{
                        const elements = chart.getElementsAtEventForMode(clickEvent, 'nearest', {{ intersect: true }}, false);
                        chart.options.onClick(clickEvent, elements, chart);
                    }}

                    console.log('Chart.js event click executed successfully');
                    return {{ success: true, method: 'chartjs_event', position: pointPosition }};

                }} catch (error) {{
                    console.error('Error in Chart.js event click:', error);
                    return {{ success: false, error: error.message }};
                }}
            }})()
            """)
            return result
        except Exception as e:
            print(f" Error in Chart.js event click: {str(e)}")
            return {'success': False, 'error': str(e)}

    async def debug_chart_clickability(self, page, chart_id, point_data):
        """Debug function to check if chart area is clickable"""
        try:
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')

            if screen_x and screen_y:
                # Check what element is at the click coordinates
                element_info = await page.evaluate(f"""
                    () => {{
                        const element = document.elementFromPoint({screen_x}, {screen_y});
                        if (element) {{
                            return {{
                                tagName: element.tagName,
                                className: element.className,
                                id: element.id,
                                isCanvas: element.tagName === 'CANVAS',
                                boundingRect: element.getBoundingClientRect(),
                                visible: element.offsetParent !== null
                            }};
                        }}
                        return null;
                    }}
                """)

                if element_info:
                    print(f"Debug - Element at ({screen_x}, {screen_y}):")
                    print(f"   Tag: {element_info.get('tagName', 'Unknown')}")
                    print(f"   Class: {element_info.get('className', 'None')}")
                    print(f"   Canvas: {element_info.get('isCanvas', False)}")
                    print(f"   Visible: {element_info.get('visible', False)}")
                else:
                    print(f"Debug - No element found at coordinates ({screen_x}, {screen_y})")

        except Exception as e:
            print(f"Debug function failed: {e}")
                   
     
    async def process_single_point_task(self, task, target_month_year):
        """Process a single point task - unchanged from your original"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        chart_id = task['chart_id']
        point_data = task['point_data']
        browser_id = task['browser_id']
        
        async with async_playwright() as playwright:
            browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)
            
            try:
                # Navigate to SpecialMetrics
                await page.goto("https://sampackag.fixedops.cc/SpecialMetrics", timeout=30000)
                # await page.wait_for_load_state("networkidle", timeout=15000)
                await asyncio.sleep(2)                
                # Click on the specific data point and extract data
                point_label = point_data.get('xLabel', 'Unknown')
                dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')                
                print(f"🎯 {task_id}: Clicking point {point_label} from {dataset_label}")                
                extracted_data = await self.click_and_extract_data(page, point_data, target_month_year)
                
                result = {
                    'task_id': task_id,
                    'chart_id': chart_id,
                    'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                    'target_month_year': task['target_month_year'],
                    'point_data': point_data,
                    'dataset_label': dataset_label,
                    'extracted_data': extracted_data,
                    'timestamp': datetime.now().isoformat(),
                    'browser_id': browser_id,
                    'success': extracted_data.get('click_success', False) if extracted_data else False
                }                
                print(f" {task_id}: Successfully processed {point_label} from {dataset_label}")
                return result                
            except Exception as e:
                print(f" {task_id}: Error processing point: {e}")                
                error_result = {
                    'task_id': task_id,
                    'chart_id': chart_id,
                    'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                    'target_month_year': task['target_month_year'],
                    'point_data': point_data,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat(),
                    'browser_id': browser_id,
                    'success': False
                }
                return error_result                
            finally:
                await context.close()
                await browser.close()
     
    async def process_chart_combination(self, combination, target_month_year, browser_id):
        """Process a single chart-point combination in its own browser"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]
        chart_id = combination.get('chart_id', 'unknown')
        target_month = combination.get('target_month_year', 'unknown')
        matching_points = combination.get('matching_points', [])        
        print(f"Browser {browser_id}: Processing {chart_id} - {target_month}")
        print(f"   Points to process: {len(matching_points)}")
        
        async with async_playwright() as playwright:
            browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)
            
            try:
                # Navigate to SpecialMetrics
                await page.goto("https://sampackag.fixedops.cc/SpecialMetrics", timeout=30000)
                # await page.wait_for_load_state("networkidle", timeout=15000)
                await asyncio.sleep(2)                
                results = []                
                # Process each matching point in this combination
                for point_idx, point in enumerate(matching_points):
                    try:
                        point_label = point.get('xLabel', f'Point_{point_idx}')
                        point_value = point.get('value', 'unknown')                        
                        print(f"🎯 Browser {browser_id}: Processing point {point_idx + 1}/{len(matching_points)}")
                        print(f"   Point: {point_label} (Value: {point_value})")                        
                        # Click on the data point and extract data
                        extracted_data = await self.click_and_extract_data(page, point, target_month_year)
                        
                        result = {
                            'chart_id': chart_id,
                            'chart_title': combination['chart_info'].get('chartTitle', 'Unknown'),
                            'target_month_year': target_month,
                            'clicked_point': point,
                            'extracted_data': extracted_data,
                            'timestamp': datetime.now().isoformat(),
                            'browser_id': browser_id,
                            'point_index': point_idx,
                            'success': extracted_data.get('click_success', False) if extracted_data else False
                        }                        
                        results.append(result)
                        print(f" Browser {browser_id}: Successfully processed point {point_label}")
                        
                        # Wait before processing next point
                        await asyncio.sleep(3)
                    
                    except Exception as e:
                        print(f" Browser {browser_id}: Error processing point {point.get('xLabel', 'unknown')}: {e}")
                        
                        error_result = {
                            'chart_id': chart_id,
                            'chart_title': combination['chart_info'].get('chartTitle', 'Unknown'),
                            'target_month_year': target_month,
                            'clicked_point': point,
                            'error': str(e),
                            'timestamp': datetime.now().isoformat(),
                            'browser_id': browser_id,
                            'point_index': point_idx,
                            'success': False
                        }
                        results.append(error_result)                
                combination['processing_status'] = 'completed'
                combination['results'] = results                
                print(f" Browser {browser_id}: Completed {chart_id} - {len(results)} results")
                return combination                
            except Exception as e:
                print(f" Browser {browser_id}: Error processing combination {chart_id}: {e}")
                combination['processing_status'] = 'failed'
                combination['error'] = str(e)
                return combination                
            finally:
                await context.close()
                await browser.close()
    
    async def run_complete_process(self):
        """Run the complete chart processing workflow with enhanced legend control"""
        print("🚀 Starting complete chart processing workflow with enhanced legend control...")

        try:
            # Step 1: Create chart-point combinations            
            combinations = await self.create_chart_point_combinations(TARGET_MONTHS_YEARS)            
            if not combinations:
                print(" No chart-point combinations found")
                return None
            print(f" Created {len(combinations)} chart-point combinations")
            # Step 2: Process all combinations in parallel with 3 browsers
            print("Step 2: Processing combinations in parallel with 3 browsers...")
            results = await self.process_all_combinations_parallel(combinations)
            if not results:
                print(" No results from processing")
                return None
            # Step 3: Save results
            print("Step 3: Saving results...")
            await self.save_results(results)
            print(" Complete chart processing workflow finished successfully")
            print(f"Final Summary:")
            print(f"   - Total combinations processed: {len(combinations)}")
            print(f"   - Total tasks completed: {results.get('total_processed', 0)}")
            print(f"   - Success rate: {results.get('success_rate', 0):.1f}%")
            return results
        except Exception as e:
            print(f" Error in complete process: {e}")            
            traceback.print_exc()
            return None
    
    async def save_results(self, results):
        """Save processing results to files with enhanced formatting"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # Create results directory if it doesn't exist
            results_dir = "chart_processing_results"
            os.makedirs(results_dir, exist_ok=True)

            # Save all results (combined)
            if results.get('all_results'):
                all_results_file = os.path.join(results_dir, f"chart_processing_all.json")
                with open(all_results_file, 'w', encoding='utf-8') as f:
                    json.dump(results['all_results'], f, indent=2, default=str, ensure_ascii=False)
                print(f"All results saved to {all_results_file}")

            # Save enhanced summary
            summary = {
                'timestamp': timestamp,
                'processing_method': 'enhanced_legend_control_parallel',
                'target_months_years': TARGET_MONTHS_YEARS,
                'max_concurrent_browsers': MAX_CONCURRENT_BROWSERS,
                'total_processed': results.get('total_processed', 0),
                'total_charts': results.get('total_charts', 0),
                'rounds_processed': results.get('rounds_processed', 0),
                'successful_count': len(results.get('successful', [])),
                'failed_count': len(results.get('failed', [])),
                'success_rate': results.get('success_rate', 0),
                'processing_statistics': {
                    'charts_with_data': results.get('total_charts', 0),
                    'average_points_per_chart': results.get('total_processed', 0) / results.get('total_charts', 1) if results.get('total_charts', 0) > 0 else 0,
                    'total_rounds': results.get('rounds_processed', 0)
                }
            }

            # Perform comparison with Special Metrics results
            # print("\nStep 4: Performing UI vs DB comparison...")
            # await self.compare_with_cp_overview_results(results, timestamp)

        except Exception as e:
            print(f" Error saving results: {e}")            
            traceback.print_exc()

    async def compare_with_cp_overview_results(self, results, timestamp):
        """Compare chart processing results with CP overview DB calculated values"""
        try:
            print("Starting comparison with CP overview results...")
          
            db_json_path='chart_processing_results/db_calculated_value.json'
            ui_json_path='chart_processing_results/chart_processing_all.json'
            # Perform comparison - fix parameter order: ui_json_path first, then db_json_path           
            
            comparison_results = self.compare_ui_db_values(ui_json_path, db_json_path)
            # Save comparison results
            await self.save_comparison_results(comparison_results, timestamp)

            print(" UI vs DB comparison completed successfully")

        except Exception as e:
            print(f" Error in comparison: {e}")            
            traceback.print_exc()

    def find_latest_cp_overview_file(self):
        """Find the latest CP overview results file"""
        try:
            # Look for cp_overview files in current directory and results directory
            search_patterns = [
                "cp_overview_single_month_results.json",
                "cp_overview_12_months_results.json",
                "chart_processing_results/db_calculated_values.json"
            ]

            latest_file = None
            latest_time = 0

            for pattern in search_patterns:
                if '*' in pattern:
                    # Handle wildcard patterns                    
                    files = glob.glob(pattern)
                    for file in files:
                        if os.path.exists(file):
                            file_time = os.path.getmtime(file)
                            if file_time > latest_time:
                                latest_time = file_time
                                latest_file = file
                else:
                    # Handle exact file names
                    if os.path.exists(pattern):
                        file_time = os.path.getmtime(pattern)
                        if file_time > latest_time:
                            latest_time = file_time
                            latest_file = pattern

            return latest_file

        except Exception as e:
            print(f" Error finding CP overview file: {e}")
            return None

    def extract_ui_values_from_results(self, results):
        """Extract UI values and chart details from chart processing results"""
        ui_values = {}
        chart_details = {}

        try:
            successful_results = results.get('successful', [])

            for result in successful_results:
                if 'extracted_data' in result and 'extraction_data' in result['extracted_data']:
                    extraction_data = result['extracted_data']['extraction_data']
                    target_month = result.get('target_month_year', 'Unknown')

                    # Extract chart information
                    chart_title = result.get('chart_title', 'Unknown Chart')
                    chart_id = result.get('chart_id', 'Unknown ID')
                    chart_name_with_id = f"{chart_title}({chart_id})"

                    # Also try to get chart info from chart_info if available
                    if 'chart_info' in result:
                        chart_info = result['chart_info']
                        if 'chartNameWithId' in chart_info:
                            chart_name_with_id = chart_info['chartNameWithId']
                        elif 'chartId' in chart_info and 'chartTitle' in chart_info:
                            chart_title = chart_info['chartTitle']
                            chart_id = chart_info['chartId']
                            chart_name_with_id = f"{chart_title}({chart_id})"

                  
                    dataset_label = "Unknown Line"
                    if 'point_data' in result:
                        point_data = result['point_data']
                        if 'datasetLabel' in point_data:
                            dataset_label = point_data['datasetLabel']

                    if 'mui_grid_data' in extraction_data:
                        for container in extraction_data['mui_grid_data']:
                            if 'items' in container:
                                for item in container['items']:
                                    title = item.get('title', '')
                                    value = item.get('value', '')

                                    # Clean the value (remove $ and commas)
                                    clean_value = value.replace('$', '').replace(',', '').replace('%', '').strip()

                                    # Store the value with month and title as key
                                    key = f"{title} ({target_month})"
                                    if key not in ui_values:
                                        ui_values[key] = clean_value

                                        # Store chart details for this key
                                        chart_details[key] = {
                                            'chart_title': chart_title,
                                            'chart_id': chart_id,
                                            'chart_name_with_id': chart_name_with_id,
                                            'dataset_label': dataset_label,
                                            'target_month': target_month,
                                            'drilldown_label': title
                                        }

            print(f"Extracted {len(ui_values)} UI values from chart processing results")
            return ui_values, chart_details

        except Exception as e:
            print(f" Error extracting UI values: {e}")
            return {}, {}

    def extract_db_values_from_cp_overview(self, cp_data):
        """Extract DB values from CP overview data"""
        db_values = {}

        try:
           
            summary_data = None
            summary_data = cp_data['cp_summary_overview'].get('summary_format', {})

            if summary_data:
                for metric, values_list in summary_data.items():
                    for value_str in values_list:
                        # Extract value and month from format: "value (YYYY-MM-DD)"
                        if '(' in value_str and ')' in value_str:
                            db_value_part = value_str.split('(')[0].strip()
                            month_part = value_str.split('(')[1].replace(')', '').strip()

                            # Clean DB value
                            clean_value = db_value_part.replace('$', '').replace(',', '').replace('%', '').strip()

                            # Create key matching UI format
                            key = f"{metric} ({month_part})"
                            db_values[key] = clean_value

            print(f"Extracted {len(db_values)} DB values from CP overview data")
            return db_values

        except Exception as e:
            print(f" Error extracting DB values: {e}")
            return {}

   
    def compare_ui_db_values(self, ui_json_path, db_json_path):
        try:
            print(f"Loading UI data from: {ui_json_path}")
            print(f"Loading DB data from: {db_json_path}")

            with open(ui_json_path, 'r') as f:
                ui_data = json.load(f)

            with open(db_json_path, 'r') as f:
                db_data = json.load(f)

            print(f"UI data type: {type(ui_data)}")
            print(f"DB data type: {type(db_data)}")

            if isinstance(ui_data, list):
                print(f"UI data is a list with {len(ui_data)} items")
                if ui_data:
                    print(f"First UI item keys: {list(ui_data[0].keys()) if isinstance(ui_data[0], dict) else 'Not a dict'}")
            elif isinstance(ui_data, dict):
                print(f"UI data keys: {list(ui_data.keys())}")

            if isinstance(db_data, dict):
                print(f"DB data keys: {list(db_data.keys())}")
                # Print first few characters of the JSON for structure analysis
                print(f"DB data sample: {str(db_data)[:500]}...")
            elif isinstance(db_data, list):
                print(f"DB data is a list with {len(db_data)} items")
            else:
                print(f"Unexpected DB data type: {type(db_data)}")
                print(f"DB data content: {str(db_data)[:500]}...")
        except Exception as e:
            print(f" Error loading JSON files: {e}")
            return []

        # Handle DB data - extract values from the new structure
        db_flat_map = {}
        monthly_data = {}

        if isinstance(db_data, dict):
            print(f"Processing DB data as dictionary with {len(db_data)} keys")

            # Extract target_month_results section
            target_month_results = db_data.get('target_month_results', {})
            analysis_info = db_data.get('analysis_info', {})
            
            if target_month_results:
                print(f"Found target_month_results with {len(target_month_results)} fields")
                
                # Get target month for date formatting
                target_month = analysis_info.get('target_month', '2023-11-01')
                formatted_date = target_month  # This should match the UI date format
                
                print(f"Target month: {target_month}, Formatted date: {formatted_date}")

               # Get customer_pay_metrics for fields that are stored there
                customer_pay_metrics = target_month_results.get("customer_pay_metrics", {})

                # Create monthly_data structure for line comparison
                monthly_data[formatted_date] = {
                    "labor_revenue": target_month_results.get("labor_revenue", 0),
                    "parts_revenue": target_month_results.get("parts_revenue", 0),
                    "combined_revenue": target_month_results.get("combined_revenue", 0),
                    "labor_gross_profit": target_month_results.get("labor_gross_profit", 0),
                    "parts_gross_profit": target_month_results.get("parts_gross_profit", 0),
                    "combined_gross_profit": target_month_results.get("combined_gross_profit", 0),
                    "labor_gross_profit_percentage": target_month_results.get("labor_gross_profit_percentage", 0),
                    "parts_gross_profit_percentage": target_month_results.get("parts_gross_profit_percentage", 0),
                    "combined_gross_profit_percentage": target_month_results.get("combined_gross_profit_percentage", 0),
                    "labor_sold_hours": target_month_results.get("labor_sold_hours", 0),
                    "effective_labor_rate_cp": customer_pay_metrics.get("effective_labor_rate", 0),  # From customer_pay_metrics
                    "cp_parts_markup_cp": customer_pay_metrics.get("parts_markup", 0)  # From customer_pay_metrics
                }


                # Create db_flat_map for drilldown comparison
                # Map the DB fields to the expected format for drilldown comparison
                field_mappings = {
                    "Labor Sale - Customer Pay": customer_pay_metrics.get("labor_sale_customer_pay", 0),
                    "Total Parts Sales": customer_pay_metrics.get("total_parts_sale", 0),
                    "Combined Revenue": target_month_results.get("combined_revenue", 0),
                    "Labor Gross Profit": target_month_results.get("labor_gross_profit", 0),
                    "Parts Gross Profit": target_month_results.get("parts_gross_profit", 0),
                    "Combined Gross Profit": target_month_results.get("combined_gross_profit", 0),
                    "Labor Gross Profit %": target_month_results.get("labor_gross_profit_percentage", 0),
                    "Parts Gross Profit %": target_month_results.get("parts_gross_profit_percentage", 0),
                    "Combined Gross Profit %": target_month_results.get("combined_gross_profit_percentage", 0),
                    "Labor Sold Hours": target_month_results.get("labor_sold_hours", 0),
                    "Total Parts Cost": customer_pay_metrics.get("total_parts_cost", 0),
                    "Total Labor Cost": customer_pay_metrics.get("total_labor_cost", 0),
                    "ELR": customer_pay_metrics.get("effective_labor_rate", 0),  # Effective Labor Rate from customer_pay_metrics
                    "Parts Markup": customer_pay_metrics.get("parts_markup", 0)  # Parts Markup from customer_pay_metrics
                }

                # Create the flat map with date keys
                for field_name, value in field_mappings.items():
                    db_key = f"{field_name} ({formatted_date})"
                    db_flat_map[db_key] = str(value)
                    print(f"Mapped: {db_key} = {value}")

            else:
                print("No target_month_results found in DB data")
        else:
            print(f"Expected DB data to be a dictionary, got {type(db_data)}")
            return []

        print(f"Created DB flat map with {len(db_flat_map)} entries")
        print(f"Created monthly_data for {len(monthly_data)} months")
        if db_flat_map:
            print(f"DB flat map keys: {list(db_flat_map.keys())[:10]}...")  # Show first 10 keys

        if not db_flat_map:
            print("No DB data found. Creating comparison with 'Not Found' values.")

        comparison_results = []

        if not isinstance(ui_data, list):
            print(f"Expected UI data to be a list, got {type(ui_data)}")
            return []

        print(f"Processing {len(ui_data)} UI charts")

        for chart_idx, chart in enumerate(ui_data):
            try:
                if not isinstance(chart, dict):
                    print(f"Chart {chart_idx} is not a dictionary, skipping")
                    continue

                chart_name_with_id = f"{chart.get('chart_title', 'Unknown')}({chart.get('chart_id', 'Unknown')})"
                line_name = chart.get("dataset_label", "Unknown Line")
                formatted_date = chart.get("target_month_year", "Unknown Date")

                # Line value for first comparison
                if "%" in line_name:
                    raw_value = chart.get("point_data", {}).get("value", "0")
                    ui_line_value = float(raw_value) * 100
                else:
                    raw_value = chart.get("point_data", {}).get("value", "0")
                    ui_line_value = float(raw_value) 
                ui_line_value_clean = str(ui_line_value).replace("$", "").replace(",", "").replace("%", "").strip()

                # FIRST COMPARISON: Compare ui_line_value with monthly_data
                line_match = False
                monthly_db_value = "Not Found"

                if formatted_date in monthly_data:
                    month_data = monthly_data[formatted_date]

                    # Map line names to monthly_data keys
                    line_to_monthly_mapping = {
                        "Labor Revenue": "labor_revenue",
                        "Parts Revenue": "parts_revenue",
                        "Combined Revenue": "combined_revenue",
                        "Labor Gross Profit": "labor_gross_profit",
                        "Parts Gross Profit": "parts_gross_profit",
                        "Combined Gross Profit": "combined_gross_profit",
                        "Labor Gross Profit %": "labor_gross_profit_percentage",
                        "Parts Gross Profit %": "parts_gross_profit_percentage",
                        "Combined Gross Profit %": "combined_gross_profit_percentage",
                        "Labor Sold Hours": "labor_sold_hours",
                        "Effective Labor Rate": "effective_labor_rate_cp",
                        "Parts Markup": "cp_parts_markup_cp"
                    }

                    monthly_key = line_to_monthly_mapping.get(line_name, None)
                    if monthly_key and monthly_key in month_data:
                        monthly_db_value = str(month_data[monthly_key])

                        # Compare values
                        try:
                            ui_floats = float(ui_line_value_clean) if ui_line_value_clean else 0
                            db_floats = float(monthly_db_value) if monthly_db_value else 0
                            line_match = abs(ui_floats - db_floats) < 0.01
                        except ValueError:
                            line_match = ui_line_value_clean == monthly_db_value

                        print(f"Line comparison: {line_name} -> UI: {ui_line_value_clean}, DB: {monthly_db_value}, Match: {line_match}")
                    else:
                        print(f"No monthly_data mapping found for line: {line_name}")
                else:
                    print(f"Date {formatted_date} not found in monthly_data")

                # Extract drilldown fields - only from container_index 0 or 1
                extracted_items = chart.get("extracted_data", {}).get("extraction_data", {}).get("mui_grid_data", [])

                if not extracted_items:
                    print(f"No extracted items found for chart {chart_name_with_id}")
                    continue

                # Filter for container_index 0 or 1
                container_items = []
                available_indexes = []
                for grid in extracted_items:
                    if not isinstance(grid, dict):
                        continue
                    
                    if "container_index" in grid:
                        available_indexes.append(grid["container_index"])

                    # Try container_index 0 first, then 1
                    if grid.get("container_index") in [0, 1]:
                        container_items.extend(grid.get("items", []))
                        print(f"Found {len(grid.get('items', []))} items in container_index {grid.get('container_index')} for chart {chart_name_with_id}")

                if not container_items:
                    print(f"No items found in container_index 0 or 1 for chart {chart_name_with_id}")
                    print(f"Available container indexes: {available_indexes}")
                    continue

                for item in container_items:
                    if not isinstance(item, dict):
                        continue

                    title = item.get("title", "")
                    value = item.get("value", "")

                    if not title or not value:
                        continue

                    ui_extracted_value = str(value).replace("$", "").replace(",", "").replace("%", "").strip()

                    # Map UI field names to exact DB field names
                    title_mapping = {
                        "Labor Sale - Customer Pay": "Labor Sale - Customer Pay",
                        "Total Parts Sale": "Total Parts Sales",  # Note: DB has "Sales" not "Sale"
                        "Combined Revenue": "Combined Revenue",
                        "Labor Gross Profit": "Labor Gross Profit",
                        "Parts Gross Profit": "Parts Gross Profit",
                        "Combined Gross Profit": "Combined Gross Profit",
                        "Labor Gross Profit %": "Labor Gross Profit %",
                        "Parts Gross Profit %": "Parts Gross Profit %",
                        "Combined Gross Profit %": "Combined Gross Profit %",
                        "Labor Sold Hours": "Labor Sold Hours",
                        "Total Parts Cost": "Total Parts Cost",
                        "Total Labor Cost": "Total Labor Cost",
                        "Effective Labor Rate": "ELR",  # DB uses "ELR" instead of full name
                        "Parts Markup": "Parts Markup"
                    }

                    # Get the correct DB field name
                    db_field_name = title_mapping.get(title, title)

                    # Build DB lookup key with correct field name
                    db_key = f"{db_field_name} ({formatted_date})"
                    db_value = db_flat_map.get(db_key, "Not Found")

                    # Debug: Show the lookup attempt
                    print(f"Looking up: '{db_key}' -> {db_value}")
                    if db_value == "Not Found":
                        print(f"Available keys matching pattern: {[k for k in db_flat_map.keys() if formatted_date in k]}")

                    # SECOND COMPARISON: Match drilldown extracted values with DB calculated values
                    drilldown_match = False
                    try:
                        ui_float = float(ui_extracted_value) if ui_extracted_value else 0
                        db_float = float(db_value.replace("%", "")) if db_value != "Not Found" else 0
                        drilldown_match = abs(ui_float - db_float) < 0.01
                    except ValueError:
                        drilldown_match = ui_extracted_value == db_value

                    # SINGLE MATCH: Both line comparison AND drilldown comparison must be TRUE
                    match = line_match and drilldown_match

                    result = {
                        "Chart_Namewith_id": chart_name_with_id,
                        "Line_Name_Legend": f"{line_name} ({formatted_date})",
                        "UI_Line_Data_Point_Value": ui_line_value,
                        "Drilldown_Extracted_Field": title,
                        "UI_Extracted_Value": ui_extracted_value,
                        "DB_Tooltip_Value": monthly_db_value,
                        "DB_Calculated_Field": db_field_name,  # Use the mapped DB field name
                        "DB_Calculated_Value": db_value,  # Second comparison DB value
                        "Match": "TRUE" if match else "FALSE"  # Single match result
                    }

                    comparison_results.append(result)

            except Exception as e:
                print(f" Error processing chart {chart_idx}: {e}")
                continue

        print(f"Generated {len(comparison_results)} comparison results")
        return comparison_results
    async def save_comparison_results(self, comparison_results, timestamp):
        """Save comparison results to CSV file"""
        try:
            if not comparison_results:
                print("No comparison results to save")
                return

            # Create results directory if it doesn't exist
            results_dir = "chart_processing_results"
            os.makedirs(results_dir, exist_ok=True)

            # Save comparison CSV with Excel format
            comparison_csv_file = os.path.join(results_dir, f"ui_db_comparison_{timestamp}.csv")

            with open(comparison_csv_file, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    "Chart_Namewith_id",  # Note: no underscore between Name and with
                    "Line_Name_Legend",
                    "UI_Line_Data_Point_Value",
                    "Drilldown_Extracted_Field",
                    "UI_Extracted_Value",
                    "DB_Tooltip_Value",  # Added missing comma
                    "DB_Calculated_Field",
                    "DB_Calculated_Value",
                    "Match"
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                # writer.writerow(["Chart Name(ID)", "Line Name(Month/Year)", "Tooltip Value", "Extracted Field", "Match (True/False)"])
                writer.writerows(comparison_results)

            print(f"Comparison results saved to: {comparison_csv_file}")

            # Print summary statistics
            total_comparisons = len(comparison_results)
            matches = sum(1 for result in comparison_results if result["Match"] == "TRUE")
            mismatches = total_comparisons - matches
            match_rate = (matches / total_comparisons * 100) if total_comparisons > 0 else 0

            print(f"\nComparison Summary:")
            print(f"   - Total comparisons: {total_comparisons}")
            print(f"   - Matches: {matches}")
            print(f"   - Mismatches: {mismatches}")
            print(f"   - Match rate: {match_rate:.1f}%")

            # Save detailed comparison JSON
            comparison_json_file = os.path.join(results_dir, f"ui_db_comparison_detailed_{timestamp}.json")
            with open(comparison_json_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "timestamp": timestamp,
                    "summary": {
                        "total_comparisons": total_comparisons,
                        "matches": matches,
                        "mismatches": mismatches,
                        "match_rate": match_rate
                    },
                    "detailed_results": comparison_results
                }, f, indent=2, ensure_ascii=False)

            print(f"Detailed comparison saved to: {comparison_json_file}")

        except Exception as e:
            print(f" Error saving comparison results: {e}")            
            traceback.print_exc()

def generate_ui_db_comparison_html(html_path, comparison_data, timestamp, tenant="Unknown", store="Unknown", role="Unknown"):
    """Generate HTML report for UI-DB comparison results"""   
    # Calculate statistics
    total = len(comparison_data)
    passed = sum(1 for entry in comparison_data if entry.get('Match', '').upper() == 'TRUE')
    failed = total - passed    
    # Group by chart name
    grouped_data = defaultdict(list)
    for entry in comparison_data:
        chart_name = entry.get('Chart_Namewith_id', 'Unknown Chart')
        grouped_data[chart_name].append(entry)

    html_template = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>UI vs DB Comparison Report</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body {{ padding: 20px; font-family: Arial, sans-serif; }}
            .badge-pass {{ background-color: #28a745; }}
            .badge-fail {{ background-color: #dc3545; }}
            .card-header {{ cursor: pointer; }}
            .comparison-row {{ display: flex; justify-content: space-between; margin-bottom: 10px; }}
            .ui-value {{ background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin-right: 10px; }}
            .db-value {{ background-color: #e9ecef; padding: 10px; border-radius: 5px; margin-left: 10px; }}
            .match-indicator {{ font-weight: bold; padding: 5px 10px; border-radius: 3px; }}
            .match-true {{ background-color: #d4edda; color: #155724; }}
            .match-false {{ background-color: #f8d7da; color: #721c24; }}
            .pre-json {{ background-color: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="mb-4">UI vs DB Comparison Report</h1>
            <div class="mb-4">
                <strong>Tenant:</strong> {tenant}<br>
                <strong>Store:</strong> {store}<br>
                <strong>Role:</strong> {role}<br>
                <strong>Generated At:</strong> {datetime.now().isoformat()}<br>
                <strong>Report Timestamp:</strong> {timestamp}<br>
            </div>

            <div class="d-flex gap-3 mb-4">
                <span class="badge bg-success">Passed: {passed}</span>
                <span class="badge bg-danger">Failed: {failed}</span>
                <span class="badge bg-secondary">Total: {total}</span>
                <span class="badge bg-info">Match Rate: {(passed/total*100):.1f}%</span>
            </div>

            <div class="accordion" id="reportAccordion">
    """
    for chart_idx, (chart_name, entries) in enumerate(grouped_data.items()):
        # Check if all entries for this chart pass
        chart_pass = all(entry.get('Match', '').upper() == 'TRUE' for entry in entries)
        badge_class = "badge-pass" if chart_pass else "badge-fail"
        badge_text = "All Passed" if chart_pass else "Has Failures"
        chart_id = f"chart{chart_idx}"
        html_template += f"""
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-{chart_id}">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#{chart_id}" aria-expanded="false" aria-controls="{chart_id}">
                    {chart_name} <span class="ms-3 badge {badge_class}">{badge_text}</span>
                    <small class="ms-2 text-muted">({len(entries)} comparisons)</small>
                </button>
            </h2>
            <div id="{chart_id}" class="accordion-collapse collapse" aria-labelledby="heading-{chart_id}" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        """
        for idx, entry in enumerate(entries):
            match = entry.get('Match', '').upper() == 'TRUE'
            sub_badge = "badge-pass" if match else "badge-fail"
            sub_text = "Passed" if match else "Failed"
            sub_id = f"{chart_id}-entry-{idx}"
            
            # Extract values for display
            line_name = entry.get('Line_Name_Legend', 'Unknown')
            tooltip_value = entry.get('UI_Line_Data_Point_Value', 'N/A')
            extracted_field = entry.get('Drilldown_Extracted_Field', 'Unknown Field')
            db_tooltip_value = entry.get('DB_Tooltip_Value', 'N/A')
            ui_value = entry.get('UI_Extracted_Value', 'N/A')
            db_field = entry.get('DB_Calculated_Field', 'Unknown Field')
            db_value = entry.get('DB_Calculated_Value', 'N/A')
            
            html_template += f"""
            <div class="card mb-2">
                <div class="card-header" data-bs-toggle="collapse" data-bs-target="#{sub_id}" aria-expanded="false" style="cursor:pointer;">
                    <strong>{extracted_field}</strong> ({line_name}) <span class="ms-2 badge {sub_badge}">{sub_text}</span>
                </div>
                <div id="{sub_id}" class="collapse">
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h6>Chart Information:</h6>
                                <p><strong>Line Name:</strong> {line_name}</p>
                                <p><strong>UI Tooltip Value:</strong> {tooltip_value}</p>
                                <p><strong>DB Tooltip Value:</strong> {db_tooltip_value}</p>
                            </div>
                            <div class="col-md-6">
                                <h6>Match Status:</h6>
                                <span class="match-indicator {'match-true' if match else 'match-false'}">
                                    {'✓ MATCH' if match else '✗ MISMATCH'}
                                </span>
                            </div>
                        </div>
                        
                        <div class="comparison-row">
                            <div class="ui-value flex-fill">
                                <h6>UI Extracted Value:</h6>
                                <p><strong>Field:</strong> {extracted_field}</p>
                                <p><strong>Value:</strong> {ui_value}</p>
                            </div>
                            <div class="db-value flex-fill">
                                <h6>DB Calculated Value:</h6>
                                <p><strong>Field:</strong> {db_field}</p>
                                <p><strong>Value:</strong> {db_value}</p>
                            </div>
                        </div>
                        
                        
                    </div>
                </div>
            </div>
            """

        html_template += """
                </div>
            </div>
        </div>
        """

    html_template += """
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """
    with open(html_path, "w", encoding="utf-8") as f:
        f.write(html_template)    
    print(f"HTML report generated: {html_path}")


async def generate_final_comparison_report(timestamp):
    """Generate a final consolidated comparison report with enhanced formatting"""
    try:
        print("Generating final consolidated comparison report...")
        results_dir = "chart_processing_results"
        # Find the comparison CSV file
        comparison_csv_file = os.path.join(results_dir, f"ui_db_comparison_{timestamp}.csv")
        if not os.path.exists(comparison_csv_file):
            print(f"Comparison CSV file not found: {comparison_csv_file}")
            return
        comparison_data = []
        with open(comparison_csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            comparison_data = list(reader)
        if not comparison_data:
            print("No comparison data found")
            return
        # Calculate summary statistics
        total_comparisons = len(comparison_data)
        matches = sum(1 for row in comparison_data if row.get('Match', '').lower() == 'true')
        mismatches = total_comparisons - matches
        match_rate = (matches / total_comparisons * 100) if total_comparisons > 0 else 0

        # Generate simplified final report
        final_report_file = os.path.join(results_dir, f"final_ui_db_comparison_report_{timestamp}.csv")

        with open(final_report_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # Write simplified header with only requested columns
            writer.writerow(["UI", "DB Calculated", "Matches"])

            # Write data rows with simplified format
            for row in comparison_data:
                ui_value = row.get('UI_Value', '')
                db_value = row.get('DB_Value', '')
                match_value = row.get('Match', '')
                # Format the UI and DB values with metric names for clarity
                ui_display = f"{row.get('UI_Title', '')} ({row.get('Month', '')}): {ui_value}"
                db_display = f"{row.get('DB_Metric', '')} ({row.get('Month', '')}): {db_value}"
                writer.writerow([ui_display, db_display, match_value])

        # Also create an Excel file with conditional formatting for highlighting
        try:
            # Create Excel workbook
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "UI vs DB Comparison"
            # Create styles
            gray_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
            blue_fill = PatternFill(start_color="305496", end_color="305496", fill_type="solid")
            yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
            center_alignment = Alignment(horizontal='center', vertical='center')
            bold_font = Font(bold=True)
            # Add "CP Overview" heading in first row (merged across all columns)
            ws.merge_cells('A1:I1')
            cp_overview_cell = ws['A1']
            cp_overview_cell.value = "CP Overview"
            cp_overview_cell.alignment = center_alignment
            cp_overview_cell.font = bold_font
            # Add "UI" heading in second row (columns C, D, E) with gray background
            ws.merge_cells('C2:E2')
            ui_cell = ws['C2']
            ui_cell.value = "UI"
            ui_cell.fill = gray_fill
            ui_cell.alignment = center_alignment
            ui_cell.font = bold_font

            # Add "Calculated" heading in second row (columns F, G,H) with blue background
            ws.merge_cells('F2:H2')
            calculated_cell = ws['F2']
            calculated_cell.value = "Calculated"
            calculated_cell.fill = blue_fill
            calculated_cell.alignment = center_alignment
            calculated_cell.font = bold_font

            # Define custom Excel column headers as requested
            custom_headers = [
                    "Chart Name(ID)",                # Custom heading
                    "Legend Name(Date)",         # Custom heading
                    "Tooltip Value",                 # Custom heading
                    "Extracted Field Name",               # Custom heading
                    "Extracted Value",
                    "Tooltip Value",
                    "Field Name",
                    "Value",
                    "Match (True/False)"
                ]
            # Original CSV headers mapping to custom headers
            original_headers = [
                "Chart_Namewith_id",
                "Line_Name_Legend",
                "UI_Line_Data_Point_Value",
                "Drilldown_Extracted_Field",
                "UI_Extracted_Value",
                "DB_Tooltip_Value",
                "DB_Calculated_Field",
                
                "DB_Calculated_Value",
                "Match"
            ]
            # Add custom headers to Excel (row 3)
            for col_idx, header in enumerate(custom_headers, start=1):
                # ws.cell(row=3, column=col_idx, value=header)
                header_cell = ws.cell(row=3, column=col_idx, value=header)
                header_cell.font = bold_font
            # Add data and apply conditional formatting (starting from row 4)
            for row_idx, row_data in enumerate(comparison_data, start=4):
                match_value = str(row_data.get('Match', '')).upper()
                for col_idx, original_header in enumerate(original_headers, start=1):
                    cell_value = row_data.get(original_header, '')
                    ws.cell(row=row_idx, column=col_idx, value=cell_value)
                    # Highlight row in yellow if match is FALSE
                    if match_value == 'FALSE':
                        ws.cell(row=row_idx, column=col_idx).fill = yellow_fill
            # Auto-adjust column widths (handle merged cells)
            for col_idx in range(1, len(custom_headers) + 1):
                max_length = 0
                column_letter = get_column_letter(col_idx)                
                # Check all cells in this column for maximum length
                for row in ws.iter_rows(min_col=col_idx, max_col=col_idx):
                    for cell in row:
                        if cell.value and not isinstance(cell, openpyxl.cell.MergedCell):
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass                
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            # Save Excel file
            excel_file = os.path.join(results_dir, f"final_ui_db_comparison_report_{timestamp}.xlsx")
            wb.save(excel_file)
            print(f"Excel report with custom headers and highlighting saved to: {excel_file}")

        except ImportError:
            print("openpyxl not available - Excel file with highlighting not created")
            print("💡 Install openpyxl to get Excel file with yellow highlighting: pip install openpyxl")

        print(f"Final comparison report saved to: {final_report_file}")
        # Print final summary to console
        print(f"\nFINAL COMPARISON SUMMARY:")
        print(f"   Total Comparisons: {total_comparisons}")
        print(f"    Successful Matches: {matches}")
        print(f"    Mismatches: {mismatches}")
        print(f"   Match Rate: {match_rate:.1f}%")
        if mismatches > 0:
            print(f"\n Found {mismatches} mismatches - check the final report for details")
        else:
            print(f"\n🎉 Perfect match! All UI and DB values are consistent!")
        html_report_file = os.path.join(results_dir, f"ui_db_comparison_report_{timestamp}.html")
        generate_ui_db_comparison_html(html_report_file, comparison_data, timestamp)
        return final_report_file
    except Exception as e:
        print(f" Error generating final comparison report: {e}")        
        traceback.print_exc()
        return None
    
# Main execution
async def main():
    """Main function to run the enhanced chart processing with legend control"""

    print("Starting Parallel Chart Processing Application with 3 Browsers")
    print("=" * 80)
    start_time = time.time()
    print(f"Start Time: {datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')}")
    # Initialize components
    auth_manager = AuthManager()
    processor = MultiChartParallelProcessor(
        max_browsers=3,  # Parallel processing uses 3 browsers
        auth_manager=auth_manager
    )
    print(f"   - Processing mode: Parallel (3 browsers, different charts)")
    print(f"   - Max concurrent browsers: 3")
    print(f"   - Target months/years: {TARGET_MONTHS_YEARS}")
    print(f"   - Browser timeout: {BROWSER_TIMEOUT}ms")
    print("=" * 80)
    # Check if we have valid authentication
    if not auth_manager.load_auth_state():
        print("No valid authentication found. Setting up authentication...")
        async with async_playwright() as playwright:
            success = await auth_manager.setup_authentication(playwright)
            if not success:
                print(" Authentication setup failed. Exiting.")
                return
    else:
        print(" Valid authentication found, proceeding with processing...")
    # Run the complete processing workflow
    print("\n Starting parallel chart processing workflow with 3 browsers...")
    results = await processor.run_complete_process()    
    if results:
        print("\n" + "=" * 80)
        print(f"🎉 Parallel processing with 3 browsers completed successfully!")
        print(f"Final Results:")
        print(f"   - Total tasks processed: {results.get('total_processed', 0)}")
        print(f"   - Charts processed: {results.get('total_charts', 0)}")
        print(f"   - Batches processed: {results.get('batches_processed', 0)}")
        print(f"   - Successful tasks: {len(results.get('successful', []))}")
        print(f"   - Failed tasks: {len(results.get('failed', []))}")
        print(f"   - Success rate: {results.get('success_rate', 0):.1f}%")
        print("=" * 80)
        # Additional statistics
        if results.get('successful'):
            print(f" Parallel processing completed with {len(results['successful'])} successful extractions")
        if results.get('failed'):
            print(f" {len(results['failed'])} tasks failed - check failed results file for details")

        
        # Generate final comparison report
        print("\n" + "=" * 80)
        print("GENERATING FINAL UI vs DB COMPARISON REPORT")
        print("=" * 80)
        try:
            db_calculation()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            await processor.compare_with_cp_overview_results(results, timestamp)
            # Generate final consolidated CSV report
            await generate_final_comparison_report(timestamp)
            print("\n" + "=" * 80)
            print(" FINAL COMPARISON REPORT GENERATED SUCCESSFULLY!")
            print("Check the 'chart_processing_results' directory for:")
            print(f"   - ui_db_comparison_{timestamp}.csv")
            print(f"   - ui_db_comparison_detailed_{timestamp}.json")
            print(f"   - final_ui_db_comparison_report_{timestamp}.csv")
            print("=" * 80)
            end_time = time.time()-start_time
            print(f"End Time: {end_time}")
        except Exception as comparison_error:
            print(f" Error generating comparison report: {comparison_error}")            
            traceback.print_exc()
    else:
        print(" Parallel processing failed - check logs for details")

def parse_arguments():
    """Parse command line arguments and update config"""
    global TARGET_MONTHS_YEARS

    parser = argparse.ArgumentParser(description='CP Summary Overview Validation')
    parser.add_argument('--store_id', required=True, help='Store ID')
    parser.add_argument('--store_name', required=True, help='Store Name')
    parser.add_argument('--start_date', required=True, help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end_date', required=True, help='End date (YYYY-MM-DD)')
    parser.add_argument('--fopc_month', required=True, help='FOPC month (YYYY-MM)')
    parser.add_argument('--pre_fopc_month', required=True, help='Pre-FOPC month (YYYY-MM)')
    parser.add_argument('--database_name', required=True, help='Database name')
    parser.add_argument('--working_days', type=float, required=True, help='Working days')
    parser.add_argument('--advisor', required=True, help='Advisor filter')
    parser.add_argument('--technician', required=True, help='Technician filter')
    parser.add_argument('--last_month', required=True, help='Last month (YYYY-MM)')
    parser.add_argument('--target_month_year', required=True, help='Target month year (YYYY-MM-DD)')
    parser.add_argument('--site_url', required=True, help='Site URL')
    parser.add_argument('--role', required=True, help='Role')

    args = parser.parse_args()

    # Update config with parsed arguments
    config.store_id = args.store_id
    config.store_name = args.store_name
    config.start_date = args.start_date
    config.end_date = args.end_date
    config.fopc_month = args.fopc_month
    config.pre_fopc_month = args.pre_fopc_month
    config.database_name = args.database_name
    config.working_days = args.working_days
    config.advisor = args.advisor
    config.technician = args.technician
    config.last_month = args.last_month
    config.target_month_year = [args.target_month_year]  # Convert to list as expected
    config.site_url = args.site_url
    config.role = args.role

    # Update the global TARGET_MONTHS_YEARS variable
    TARGET_MONTHS_YEARS = config.target_month_year

    print(f"Configuration loaded:")
    print(f"  Store ID: {config.store_id}")
    print(f"  Store Name: {config.store_name}")
    print(f"  Target Month Year: {config.target_month_year}")
    print(f"  Database: {config.database_name}")

def run_validation():
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(" Processing interrupted by user")
    except Exception as e:
        print(f"\n Unexpected error: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    parse_arguments()
    run_validation()
