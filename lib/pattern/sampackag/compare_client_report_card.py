"""
Compares Client Report Card (1 month) values between UI and calculated results, and generates CSV, Excel, JSON, and HTML reports.
"""

import json
import csv
import os
import re
import logging
from openpyxl.styles import Font, Alignment, PatternFill
from openpyxl import Workbook
import openpyxl
from datetime import datetime
from dotenv import load_dotenv
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")

# Constants
Tenant = config.database_name
store = config.store_name
role = config.role


def clean_number(value):
    """Remove formatting and convert to float if possible."""
    if value is None or value == "":
        return "Missing"
    if isinstance(value, str):
        value = re.sub(r"[$%,]", "", value)  # Remove $, %, and ,
        if re.match(r"\(.*\)", value):  # Check if value is in parentheses (negative number)
            value = "-" + value.strip("()")  # Convert (2) to -2
    try:
        return float(value)
    except ValueError:
        return value  # Return as is if not convertible


def extract_main_values(data):
    """Extract main KPI fields like Monthly FOPC, Total, ROI, etc."""
    keys_to_extract = ["Monthly FOPC", "Monthly DMS", "Total", "ROI", "Total Pts & Lbr GP Change", "Repair ELR Change"]

    return {key: {"Second Month": clean_number(data.get(key, "Missing"))} for key in keys_to_extract}


def extract_kpis_from_dict(data):
    """Extract KPI values from the dictionary structure."""
    kpis = {}
    for kpi, values in data.get("KPIs", {}).items():
        if isinstance(values, list) and len(values) == 3:
            kpis[kpi] = {
                "Second Month": clean_number(values[0]),
                "First Month": clean_number(values[1]),
                "Variance": clean_number(values[2]),
            }
        elif isinstance(values, dict):
            kpis[kpi] = {
                "Second Month": clean_number(values.get("Second Month", "Missing")),
                "First Month": clean_number(values.get("First Month", "Missing")),
                "Variance": clean_number(values.get("Variance", "Missing")),
            }
    return kpis


def extract_category_kpis(data, category):
    """Extract KPIs from Competitive, Maintenance, and Repair sections."""
    category_data = data.get(category, {})
    return {
        f"{category} - {kpi}": {
            "Second Month": clean_number(values.get("Second Month", "Missing")),
            "First Month": clean_number(values.get("First Month", "Missing")),
            "Variance": clean_number(values.get("Variance", "Missing")),
        }
        for kpi, values in category_data.items()
    }


# Helpers
def sanitize(name): return name.replace(" ", "-")
def get_output_folder(): return f"Playwright-report-{sanitize(Tenant)}_{sanitize(store)}_{sanitize(role)}"

def generate_playwright_style_html(html_path: str, json_report_data: list) -> None:
    """
    Generates a Bootstrap-styled HTML report from comparison results.
    """
    from collections import defaultdict

    passed = sum(1 for entry in json_report_data if entry['match'])
    failed = len(json_report_data) - passed
    total = len(json_report_data)

    # Group by section prefix
    sectioned_data = defaultdict(list)
    for entry in json_report_data:
        kpi = entry['kpi']
        if ' - ' in kpi:
            section, name = kpi.split(' - ', 1)
        else:
            section, name = 'KPI', kpi
        entry['clean_kpi'] = name
        sectioned_data[section].append(entry)

    html_template = f"""
    <!DOCTYPE html>
    <html lang=\"en\">
    <head>
        <meta charset=\"UTF-8\">
        <title>Client Report Card One Month</title>
        <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">
        <style>
            body {{ padding: 20px; font-family: Arial, sans-serif; }}
            .badge-pass {{ background-color: #28a745; }}
            .badge-fail {{ background-color: #dc3545; }}
            .card-header {{ cursor: pointer; }}
        </style>
    </head>
    <body>
        <div class=\"container\">
            <h1 class=\"mb-4\">Client Report Card One Month</h1>
            <div class=\"mb-4\">
                <strong>Tenant:</strong> {Tenant}<br>
                <strong>Store:</strong> {store}<br>
                <strong>Role:</strong> {role}<br>
                <strong>Generated At:</strong> {datetime.now().isoformat()}<br>
            </div>

            <div class=\"d-flex gap-3 mb-4\">
                <span class=\"badge bg-success\">Passed: {passed}</span>
                <span class=\"badge bg-danger\">Failed: {failed}</span>
                <span class=\"badge bg-secondary\">Total: {total}</span>
            </div>

            <div class=\"accordion\" id=\"reportAccordion\">
    """

    for s_idx, (section, entries) in enumerate(sectioned_data.items()):
        section_pass = all(entry['match'] for entry in entries)
        badge_class = "badge-pass" if section_pass else "badge-fail"
        badge_text = "Passed" if section_pass else "Failed"
        section_id = f"section{s_idx}"

        html_template += f"""
        <div class=\"accordion-item\">
            <h2 class=\"accordion-header\" id=\"heading-{section_id}\">
                <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#{section_id}\" aria-expanded=\"false\" aria-controls=\"{section_id}\">
                    {section} <span class=\"ms-3 badge {badge_class}\">{badge_text}</span>
                </button>
            </h2>
            <div id=\"{section_id}\" class=\"accordion-collapse collapse\" aria-labelledby=\"heading-{section_id}\" data-bs-parent=\"#reportAccordion\">
                <div class=\"accordion-body\">
        """

        for idx, entry in enumerate(entries):
            match = entry['match']
            sub_badge = "badge-pass" if match else "badge-fail"
            sub_text = "Passed" if match else "Failed"
            sub_id = f"{section_id}-entry-{idx}"
            html_template += f"""
            <div class=\"card mb-2\">
                <div class=\"card-header\" data-bs-toggle=\"collapse\" data-bs-target=\"#{sub_id}\" aria-expanded=\"false\" style=\"cursor:pointer;\">
                    {entry['clean_kpi']} <span class=\"ms-2 badge {sub_badge}\">{sub_text}</span>
                </div>
                <div id=\"{sub_id}\" class=\"collapse\">
                    <div class=\"card-body\">
                        <strong>UI:</strong>
                        <pre>{json.dumps(entry['ui'], indent=2)}</pre>
                        <strong>Calculated:</strong>
                        <pre>{json.dumps(entry['calculated'], indent=2)}</pre>
                    </div>
                </div>
            </div>
            """

        html_template += """
                </div>
            </div>
        </div>
        """

    html_template += """
            </div>
        </div>
        <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>
    </body>
    </html>
    """

    with open(html_path, "w", encoding="utf-8") as f:
        f.write(html_template)

def compare_client_report_card(file1_path, file2_path):
    """Compare two JSON files and save the KPI comparison results in a CSV file."""
    with open(file1_path, "r") as file1, open(file2_path, "r") as file2:
        results_data = json.load(file1)
        ui_data = json.load(file2)

    main_values_first_file = extract_main_values(results_data)
    main_values_second_file = extract_main_values(ui_data)
    kpis_first_file = extract_kpis_from_dict(results_data)
    kpis_second_file = extract_kpis_from_dict(ui_data)

    for section in ["Competitive", "Maintenance", "Repair"]:
        kpis_first_file.update(extract_category_kpis(results_data, section))
        kpis_second_file.update(extract_category_kpis(ui_data, section))

    output_data = []
    for kpi in set(kpis_first_file.keys()).union(set(kpis_second_file.keys())):
        first = kpis_first_file.get(kpi, {"Second Month": "Missing", "First Month": "Missing", "Variance": "Missing"})
        second = kpis_second_file.get(kpi, {"First Month": "Missing", "Second Month": "Missing", "Variance": "Missing"})
        is_match = (
            first["First Month"] == second["Second Month"]
            and second["First Month"] == first["Second Month"]
            and first["Variance"] == second["Variance"]
        )
        output_data.append(
            [
                kpi,
                first["First Month"],
                first["Second Month"],
                first["Variance"],
                second["Second Month"],
                second["First Month"],
                second["Variance"],
                is_match,
            ]
        )

    for kpi in set(main_values_first_file.keys()).union(set(main_values_second_file.keys())):
        first_value = main_values_first_file.get(kpi, {"Second Month": "Missing"})["Second Month"]
        second_value = main_values_second_file.get(kpi, {"Second Month": "Missing"})["Second Month"]
        is_match = first_value == second_value
        output_data.append([kpi, first_value, "N/A", "N/A", second_value, "N/A", "N/A", is_match])

    output_folder, output_csv = create_folder_file_path(base_folder_name="Individual_Reports", output_file="comparison_results_client_report_1_month.csv", tenant_name=config.database_name)
    with open(output_csv, "w", newline="", encoding="utf-8") as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(
            [
                "KPI Name",
                "First Month",
                "Second Month",
                "Variance",
                "First Month",
                "Second Month",
                "Variance",
                "Match (True/False)",
            ]
        )
        writer.writerows(output_data)

    print(f"Comparison results saved to {output_csv}")

    # Read the CSV file and create an Excel workbook
    csv_file = output_csv
    folder, xlsx_file = create_folder_file_path(base_folder_name="Individual_Reports",
                            output_file="comparison_client_report_1_month_highlighted.xlsx",
                            tenant_name=config.database_name)

    # Convert CSV to Excel
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Comparison Results"

    ws.merge_cells(start_row=1, start_column=1, end_row=1, end_column=7)
    ws.cell(row=1, column=1).value = "1 Month Client Report Card"
    ws.cell(row=1, column=1).font = Font(bold=True, size=14)
    ws.cell(row=1, column=1).alignment = Alignment(horizontal="center", vertical="center")
    # Define background fills
    ui_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")  # Light blue
    calc_fill = PatternFill(start_color="305496", end_color="305496", fill_type="solid")
    ws.merge_cells(start_row=3, start_column=2, end_row=3, end_column=4)
    ui_cell = ws.cell(row=3, column=2, value="UI")
    ui_cell.font = Font(bold=True, size=12)
    ui_cell.alignment = Alignment(horizontal="center", vertical="center")
    ui_cell.fill = ui_fill
    # Apply fill to entire merged range
    for col in range(2, 4):
        ws.cell(row=3, column=col).fill = ui_fill

        # Calculated Title (row 3, columns 8 to 13)
    ws.merge_cells(start_row=3, start_column=2, end_row=3, end_column=4)
    ui_cell = ws.cell(row=3, column=2, value="UI")
    ui_cell.font = Font(bold=True, size=12)
    ui_cell.alignment = Alignment(horizontal="center", vertical="center")
    ui_cell.fill = ui_fill
    # Apply fill to entire merged range
    for col in range(2, 4):
        ws.cell(row=3, column=col).fill = ui_fill

    # Calculated Title
    ws.merge_cells(start_row=3, start_column=5, end_row=3, end_column=7)
    calc_cell = ws.cell(row=3, column=5, value="Calculated")
    calc_cell.font = Font(bold=True, size=12)
    calc_cell.alignment = Alignment(horizontal="center", vertical="center")
    calc_cell.fill = calc_fill
    # Apply fill to entire merged range
    for col in range(5, 7):
        ws.cell(row=3, column=col).fill = calc_fill
        # Apply fill to entire merged range
        for col in range(5, 7):
            ws.cell(row=3, column=col).fill = calc_fill

    # Read CSV
    with open(csv_file, "r", encoding="utf-8") as file:
        reader = csv.reader(file)
        headers = next(reader)

        # Write headers on row 2 with bold font
        for col_idx, header in enumerate(headers, start=1):
            cell = ws.cell(row=4, column=col_idx, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal="center", vertical="center")

            # Write data starting from row 4
        for row_idx, row in enumerate(reader, start=4):
            ws.append(row)

        # Define yellow fill for highlighting
        yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")

        # Get column index of "Match (True/False)" (assumed to be in the last column)
        match_column_index = ws.max_column
        # Apply formatting to rows where "Match (True/False)" is False
        for row in ws.iter_rows(min_row=2, max_row=ws.max_row, min_col=match_column_index, max_col=match_column_index):
            for cell in row:
                if cell.value == "False":  # Check if match is False
                    for cell_to_fill in ws[cell.row]:  # Apply fill to the entire row
                        cell_to_fill.fill = yellow_fill

        # Save the formatted Excel file
        wb.save(xlsx_file)
    # print(f" Excel file with highlighted mismatches saved as {xlsx_file}")

     # # Save JSON report
    folder, json_path = create_folder_file_path(base_folder_name="Individual_Reports",
                            output_file="client_report_card_one_month.json",
                            tenant_name=config.database_name)

    with open(json_path, "w") as jf:
        json.dump({
            "tenant": Tenant,
            "store": store,
            "role": role,
            "generatedAt": datetime.now().isoformat(),
            "results": output_data
        }, jf, indent=2)

    logging.info(f"JSON report saved to {json_path}")
    
    # Save HTML report
    folder, html_path = create_folder_file_path(base_folder_name="Individual_Reports",
                            output_file="client_report_card_one_month.html",
                            tenant_name=config.database_name)
    # Convert to JSON-style structure for HTML reporting
    json_report_data = []

    # Process comparison rows from KPI section
    for row in output_data:
        kpi = row[0]
        match = row[-1]
        json_report_data.append({
            "kpi": kpi,
            "match": match,
            "ui": {
                "First Month": row[1],
                "Second Month": row[2],
                "Variance": row[3]
            },
            "calculated": {
                "First Month": row[4],
                "Second Month": row[5],
                "Variance": row[6]
            }
        })


    generate_playwright_style_html(html_path, json_report_data)
    logging.info(f"HTML report saved to {html_path}")
