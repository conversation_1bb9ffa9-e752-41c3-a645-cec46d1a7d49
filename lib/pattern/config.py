"""
Configuration module for validation scripts.
Holds all runtime parameters for validation in a single Config dataclass.
"""

from dataclasses import dataclass
from typing import Optional, Any, List

@dataclass
class Config:
    store_id: Optional[str] = None
    store_name: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    fopc_month: Optional[str] = None
    pre_fopc_month: Optional[str] = None
    database_name: Optional[str] = None
    working_days: Optional[float] = None
    advisor: Optional[str] = None
    technician: Optional[str] = None
    site_url: Optional[str] = None
    last_month: Optional[str] = None
    role: Optional[str] = None
    target_month_year: Optional[List[str]] = None
    # Database results
    all_revenue_details: Any = None
    retail_flag_all: Any = None
    menu_master_df: Any = None
    menu_service_type_df: Any = None
    assigned_menu_models_df: Any = None
    assigned_menu_opcodes_df: Any = None
    mpi_setup_df: Any = None
    mpi_opcodes: Any = None
    all_revenue_details_for_client_report_card_3_month: Any = None

# Global config instance to be used across modules
config = Config()