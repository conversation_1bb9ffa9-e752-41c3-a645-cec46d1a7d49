"""
Module to combine multiple HTML validation reports into a single consolidated HTML report.
"""

from bs4 import BeautifulSoup
import os
import re
import logging
from typing import Optional
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path

logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")


def make_ids_unique(soup: BeautifulSoup, prefix: str) -> BeautifulSoup:
    """
    Prefixes all 'id', 'href', and 'data-bs-target' attributes in the soup with the given prefix.
    This avoids conflicts between multiple reports.
    """
    # Update IDs
    for tag in soup.find_all(attrs={"id": True}):
        old_id = tag["id"]
        new_id = f"{prefix}-{old_id}"
        tag["id"] = new_id

        # Update all matching anchor href="#old_id"
        for ref_tag in soup.find_all(href=f"#{old_id}"):
            ref_tag["href"] = f"#{new_id}"

        # Update Bootstrap-specific targets
        for ref_tag in soup.find_all(attrs={"data-bs-target": f"#{old_id}"}):
            ref_tag["data-bs-target"] = f"#{new_id}"

        for ref_tag in soup.find_all(attrs={"aria-controls": old_id}):
            ref_tag["aria-controls"] = new_id

        for ref_tag in soup.find_all(attrs={"aria-labelledby": old_id}):
            ref_tag["aria-labelledby"] = new_id

    return soup

def combine_all_reports() -> None:
    folder, output_file = create_folder_file_path(base_folder_name="Final_Consolidated_Report", output_file="consolidated_report.html", tenant_name=config.database_name)
    report_folder = create_folder_file_path(base_folder_name="Individual_Reports", tenant_name=config.database_name)
    combined_sections = []
    for filename in sorted(os.listdir(report_folder)):
        if filename.endswith(".html"):
            filepath = os.path.join(report_folder, filename)
            try:
                with open(filepath, "r", encoding="utf-8") as f:
                    soup = BeautifulSoup(f, "html.parser")
                    container_div = soup.find("div", class_="container")
                    if container_div:
                        # Make IDs unique
                        unique_prefix = os.path.splitext(filename)[0].replace(" ", "_")
                        updated_container = make_ids_unique(container_div, unique_prefix)

                        section_html = f"""
                        <div class="report-section border rounded p-3 mb-5">
                            {str(updated_container)}
                        </div>
                        """
                        combined_sections.append(section_html)
            except Exception as e:
                logging.warning(f"Failed to process {filename}: {e}")

    full_html = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Combined Validation Report</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <style>
            body {{ padding: 20px; font-family: Arial, sans-serif; }}
            .badge-pass {{ background-color: #28a745; }}
            .badge-fail {{ background-color: #dc3545; }}
            .card-header {{ cursor: pointer; }}
            .report-section h2 {{ margin-bottom: 1rem; border-bottom: 1px solid #ccc; padding-bottom: 0.5rem; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="mb-5">Combined Report</h1>
            {"".join(combined_sections)}
        </div>
    </body>
    </html>
    """

    try:
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(full_html)
        logging.info(f"Combined HTML report created at: {output_file}")
    except Exception as e:
        logging.error(f"Failed to write combined report: {e}")
