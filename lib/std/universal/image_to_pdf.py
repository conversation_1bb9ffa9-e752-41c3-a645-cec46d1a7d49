from PIL import Image
import os
from dotenv import load_dotenv

load_dotenv()

def convert_image_to_pdf(img_path: str, pdf_path: str):
    """Converts an image to a PDF file."""
    try:
        image = Image.open(img_path)
        if image.mode in ("RGBA", "P"):
            image = image.convert("RGB")  # Convert to PDF-safe RGB

        image.save(pdf_path, "PDF", resolution=100.0)
        image.close()
        print(f"Successfully created PDF: {pdf_path}")
        return pdf_path
    except Exception as e:
        print(f"Error in convert_image_to_pdf: {e}")


if __name__ == "__main__":
    IMG_PATH = os.getenv("IMG_PATH")
    PDF_PATH = os.getenv("PDF_PATH")
    convert_image_to_pdf(IMG_PATH, PDF_PATH)
