import os
import re
from typing import Optional, Union
from lib.pattern.config import config

def create_folder_file_path(base_folder_name: str, output_file: Optional[str] = None, tenant_name: Optional[str] = None) -> Union[str, tuple[str, str]]:
    """
    Generate a sanitized, tenant-specific folder path, and optionally a file path.

    Args:
        base_folder_name (str): Base name for the folder.
        output_file (str, optional): File name to generate full path. If None, only folder path is returned.
        tenant_name (str, optional): Tenant name. If None, tries to get from config.

    Returns:
        Union[str, tuple[str, str]]:
            - If output_file is None: returns output_folder (str)
            - If output_file is provided: returns (output_folder, output_file_path) (tuple)
    """
    if tenant_name is None:
        try:
            tenant_name = config.database_name
        except (ImportError, AttributeError):
            raise ValueError("Missing 'database_name' in config. Cannot generate output path.")

    # Sanitize tenant name
    def sanitize_filename(name: str) -> str:
        return re.sub(r'[^\w\-_. ]', '_', name.strip())

    safe_tenant_name = sanitize_filename(tenant_name)

    # Create folder
    output_folder = f"{base_folder_name}-{safe_tenant_name}"
    os.makedirs(output_folder, exist_ok=True)

    if output_file:
        output_file_path = os.path.join(output_folder, output_file)
        return output_folder, output_file_path
    else:
        return output_folder
